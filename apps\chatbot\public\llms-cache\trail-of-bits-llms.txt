# Trail of Bits

> Since 2012, Trail of Bits has helped secure some of the world's most targeted organizations and products. We combine high-end security research with a real-world attacker mentality to reduce risk and fortify code. We don't just fix bugs, we fix software. Trail of Bits provides comprehensive security services through four primary areas of expertise: application security, blockchain, cryptography, and AI/ML. Our approach emphasizes discovering root causes of security weaknesses and providing actionable recommendations that enhance overall system resilience.

## Software assurance services

- [AI/ML Security](https://www.trailofbits.com/services/software-assurance/ai-ml/)
- [Application Security](https://www.trailofbits.com/services/software-assurance/appsec/)
- [Blockchain Security](https://www.trailofbits.com/services/software-assurance/blockchain/)
- [Cryptography](https://www.trailofbits.com/services/software-assurance/cryptography/)

## Security reviews

- [AI/ML](https://github.com/trailofbits/publications?tab=readme-ov-file#mlai-reviews)
- [Cryptography](https://github.com/trailofbits/publications?tab=readme-ov-file#cryptography-reviews)
- [Technology product](https://github.com/trailofbits/publications?tab=readme-ov-file#technology-product-reviews)
- [Cloud-native](https://github.com/trailofbits/publications?tab=readme-ov-file#cloud-native-reviews)
- [Invariant testing and development](https://github.com/trailofbits/publications?tab=readme-ov-file#invariant-testing-and-development-engagements)
- [Blockchain](https://github.com/trailofbits/publications?tab=readme-ov-file#blockchain-reviews)

## Security engineering

- [Custom Software Development](https://www.trailofbits.com/services/security-engineering/)
- [Open Source Security](https://www.trailofbits.com/services/security-engineering/)

## Research and development

- [Applied Research](https://www.trailofbits.com/services/research-and-development/)
- [AI Grand Cyber Challenge Finalist](https://blog.trailofbits.com/2024/08/12/trail-of-bits-advances-to-aixcc-finals/)

## Resources

- [Academic papers](https://github.com/trailofbits/publications?tab=readme-ov-file#academic-papers)
- [Conference presentations](https://github.com/trailofbits/publications?tab=readme-ov-file#conference-presentations)
- [Guides and Handbooks](https://github.com/trailofbits/publications?tab=readme-ov-file#guides-and-handbooks)
- [Datasets](https://github.com/trailofbits/publications?tab=readme-ov-file#datasets)
- [Podcasts](https://github.com/trailofbits/publications?tab=readme-ov-file#podcasts)
- [Public comments](https://github.com/trailofbits/publications?tab=readme-ov-file#public-comments)
- [Disclosures](https://github.com/trailofbits/publications?tab=readme-ov-file#disclosures)
- [Workshops](https://github.com/trailofbits/publications?tab=readme-ov-file#workshops)
- [Research reports](https://github.com/trailofbits/publications?tab=readme-ov-file#research-reports)
- [Application security testing handbook](https://appsec.guide/)
- [Building Secure Contracts handbook](https://secure-contracts.com/not-so-smart-contracts/cosmos/)
- [ZKdocs](https://www.zkdocs.com/)

## Popular blockchain tools

- [Slither](https://github.com/trailofbits/publications?tab=readme-ov-file#academic-papers)
- [Medusa](https://github.com/crytic/medusa)
- [Attacknet](https://github.com/crytic/attacknet)

## Popular cryptography tools

- [Decree](https://github.com/trailofbits/decree)

## Popular AI/ML tools

- [PrivacyRaven](https://github.com/trailofbits/PrivacyRaven)
- [Fickling](https://github.com/trailofbits/fickling)

## Popular application security tools and rules

- [Semgrep rules](https://github.com/trailofbits/semgrep-rules)
- [Ruzzy](https://github.com/trailofbits/ruzzy)

## Other popular tools

- [WeAudit](https://marketplace.visualstudio.com/items?itemName=trailofbits.weaudit)
- [SARIF Explorer](https://marketplace.visualstudio.com/items?itemName=trailofbits.sarif-explorer)


## Contact and socials

- [Contact Us](https://www.trailofbits.com/contact/)
- [Blog](https://blog.trailofbits.com/)
- [Twitter](https://twitter.com/trailofbits)
- [LinkedIn](https://www.linkedin.com/company/trail-of-bits)
- [GitHub](https://github.com/trailofbits/)
- [Community Forum](https://slack.empirehacking.nyc/)

## Optional

- [Careers](https://www.trailofbits.com/careers/)
- [Resources](https://www.trailofbits.com/resources/)
- [Built In Best Places](https://www.trailofbits.com/)
- [Forrester Recognition](https://www.trailofbits.com/forrester/)