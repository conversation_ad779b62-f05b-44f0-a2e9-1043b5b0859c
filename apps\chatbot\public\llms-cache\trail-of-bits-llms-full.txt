# Trail of Bits

> Since 2012, Trail of Bits has helped secure some of the world's most targeted organizations and products. We combine high-end security research with a real-world attacker mentality to reduce risk and fortify code. We don't just fix bugs, we fix software. Trail of Bits provides comprehensive security services through four primary areas of expertise: application security, blockchain, cryptography, and AI/ML. Our approach emphasizes discovering root causes of security weaknesses and providing actionable recommendations that enhance overall system resilience.

For historical content and publications prior to 2022, please refer to our [GitHub repository](https://github.com/trailofbits/publications).

## Software assurance services

- [AI/ML Security](https://www.trailofbits.com/services/software-assurance/ai-ml/): Comprehensive security reviews of AI systems including model assessment, deployment security, and privacy analysis
AI/ML Security Services
1. Security & Safety Training
   * Comprehensive security training for AI-based system risks
   * Coverage of AI failure modes and adversarial attacks
   * Training on AI safety principles
   * Data provenance and pipeline threats analysis
   * Risk mitigation strategy development
   * Custom training solutions based on client needs

2. ML-Ops and Pipeline Assessment
   * Complete AI/ML pipeline evaluation
   * Software architecture components
   * ML architecture review (PyTorch, etc.)
   * CI/CD processes assessment
   * Data provenance verification
   * Hardware stacks evaluation (GPUs)
   * Novel attack vector identification

3. AI Risk Assessment
   * Threat modeling for AI systems
   * Operational design domain application
   * Scenario analysis for functional risks
   * Assessment of existing risk frameworks
   * AI adoption risk evaluation

4. Model Capabilities Evaluation
   * Assessment of first and third-party AI models
   * Offensive and defensive cyber capabilities testing
   * Performance benchmarking against experts
   * State-of-the-art tool comparison
   * AI red teaming expertise
   * Integration guidance for security processes


- [Application Security](https://www.trailofbits.com/services/software-assurance/appsec/): Full-spectrum application security services including architecture review and penetration testing
1. Design Assessment
   * One to two-week focused security analysis
   * Early system design phase evaluation
   * Security architecture review
   * Design goals and proactive risk mitigation
   * Business strategy alignment assessment
   * Custom development guidance
   * Codebase forking evaluation

2. Threat Modeling
   * Data*centric threat models development
   * Security controls maturity evaluation using traffic*light protocol
   * Component and trust zone identification
   * System element classification
   * Threat actor path analysis
   * Detailed system diagramming
   * Security control assessment

3. Cloud/Infrastructure Assessment 
   * Cloud*hosted applications evaluation
   * Infrastructure deployment analysis
   * Automated analysis using specialized tools:
     * Terrascan
     * Kubediff
     * ScoutSuite
     * tfsec
   * Custom rule development
   * Container and orchestration security
   * CI/CD pipeline assessment

4. Comprehensive Code Assessment
   * Hybrid approach combining:
     * Manual assessment
     * Static analysis (CodeQL, Semgrep)
     * Dynamic analysis
   * High-risk component evaluation across:
     * Core project code
     * Infrastructure code
     * Front end and back end
     * APIs and SDKs
   * Code resilience enhancement
   * Strategic security guidance

- [Blockchain Security](https://www.trailofbits.com/services/software-assurance/blockchain/): Advanced blockchain security covering smart contracts, protocols, and cryptographic implementations
1. Design Assessment
   * System architecture analysis
   * Component specifications review
   * Strategic solution analysis
   * Component*level recommendations
   * Advanced testing techniques guidance
   * Customized client solutions
   * Risk mitigation strategies
   * Security tool integration guidance

2. Early Stage Assessment
   * Guidance for early SDLC projects
   * Lightweight code review
   * Architecture recommendations
   * Risk mitigation planning
   * Security gap identification
   * Codebase maturity evaluation
   * Documentation and testing review
   * Access control evaluation

3. Invariant Testing & Development
   * Invariant identification and development
   * System*level invariant specification
   * Fuzzing initialization and setup
   * Testing integration services
   * Developer training and guidance
   * Cloud infrastructure setup
   * Long*term fuzzing campaign support

4. Comprehensive Code Assessment
   * Smart contract analysis
   * Business logic evaluation
   * L1/L2 node review
   * Bridge security assessment
   * Off-chain component review
   * Code maturity analysis
   * Automated tools integration
   * Long-term security recommendations

- [Cryptography](https://www.trailofbits.com/services/software-assurance/cryptography/): Expert cryptographic review and implementation security assessment
1. Cryptographic Design Assessment
   * Protocol and algorithm security review
   * Initial manual assessment
   * Design clarification and improvement
   * Standard practices evaluation
   * Threat model development
   * Automated analysis with specialized tools:
     * Verifpal
     * ProVerif
     * CryptoVerif
     * Tamarin

2. Cryptographic Code Assessment
   * Standard cryptography evaluation
   * Zero*knowledge proof systems review
   * Threshold signature schemes analysis
   * Multi*party computation assessment
   * E2EE protocol review
   * Cloud cryptography evaluation
   * Hardware*based cryptography assessment
   * Rust and Go cryptography expertise

3. Cryptographic Engineering
   * Secure cryptographic solution development
   * Detailed specification creation
   * Implementation security
   * Comprehensive documentation
   * Safe API development
   * Thorough testing procedures
   * Four service variants:
     * New design and engineering
     * Existing design implementation
     * Legacy system enhancement
     * Design specification development


## Security Reviews

### AI/ML Reviews
- [YOLOv7 Threat Model and Code Review](https://github.com/WongKinYiu/yolov7/): In-depth security assessment of popular vision model
- [EleutherAI, Hugging Face, & Stability AI SafeTensors](https://github.com/huggingface/safetensors): Security review of ML model serialization format
- [Hugging Face Gradio](https://huggingface.co/gradio): Security assessment of ML GUI framework

### Recent Academic Papers
- [A Broad Comparative Evaluation of Software Debloater Tools](https://www.usenix.org/conference/usenixsecurity24): USENIX Security 2024
- [PolyTracker: Whole-Input Dynamic Information Flow Tracing](https://conf.researchr.org/details/issta-ecoop-2024/issta-ecoop-2024-tool-demonstrations/7/PolyTracker-Whole-Input-Dynamic-Information-Flow-Tracing): ISSTA 2024
- [Endokernel: A Thread Safe Monitor](https://www.usenix.org/conference/usenixsecurity24/presentation/yang-fangfei): USENIX Security 2024
- [Design and Implementation of a Coverage-Guided Ruby Fuzzer](https://cset24.isi.edu/): CSET 24
- [Test Harness Mutilation](https://conf.researchr.org/home/<USER>/mutation-2024): Mutation 2024
- [VAST: MLIR compiler for C/C++](https://llvm.swoogo.com/2024eurollvm): EuroLLVM 2024
- [Careful with MAc-then-SIGn](https://www.ieee-security.org/TC/EuroSP2023/index.html): Euro S&P 2023 
- [Weak Fiat-Shamir Attacks](https://eprint.iacr.org/2023/691): IEEE S&P 2023
- [Endoprocess: Programmable Subprocess Isolation](https://www.nspw.org/2023/program): NSPW 2023
- [CIVSCOPE: Analyzing Memory Corruption](https://dl.acm.org/doi/abs/10.1145/3625275.3625399): SOSP KISV 2023
- [Detecting variability bugs](https://langsec.org/spw23/papers.html#variability): LangSec 2023
- [Blind Spots: Detecting Ignored Inputs](https://arxiv.org/abs/2301.08700): LangSec 2023

### Cryptography Reviews
- [Aligned](https://www.alignedlayer.com/): Security analysis of Layer 2 scaling solution
- [Lit Protocol Cait-Sith](https://www.litprotocol.com/): Threshold cryptography review
- [Discord DAVE Protocol](https://discord.com/): Protocol security assessment
- [Scroll zstd Compression](https://scroll.io/): Compression security review
- [Iron Fish FishHash](https://ironfish.network/): Hash function security analysis
- [Scroll ZkEVM 4844 Blob](https://scroll.io/): ZK-proof system review
- [Ockam](https://docs.ockam.io): Cryptographic design review
- [Aleo snarkVM](https://www.aleo.org/): Zero-knowledge proof system analysis
- [Microsoft/Verasion Go-COSE](https://github.com/veraison): COSE implementation review

### Technology Product Reviews
- [RubyGems.org](https://www.rubygems.org): Package registry security assessment
- [Kraken Wallet Series](https://www.kraken.com/wallet): Multiple wallet security reviews
- [Hugging Face Gradio](https://huggingface.co/gradio): ML framework security audit
- [Eclipse Temurin](https://adoptium.net/temurin/): Java runtime security review
- [Arch Linux Pacman](https://archlinux.org/pacman/): Package manager security audit
- [cURL HTTP3](https://curl.se/): Protocol implementation review
- [Lisk SDK Series](https://lisk.com/): Blockchain platform security reviews
- [DragonFly2](https://d7y.io/): Distributed system security assessment
- [Eclipse JKube](https://eclipse.dev/jkube/): Container tooling security review

### Cloud-native Reviews
- [KEDA](https://keda.sh/): Kubernetes autoscaling security assessment
- [Terraform Enterprise](https://developer.hashicorp.com/terraform/enterprise): Infrastructure security review
- [Nomad Enterprise](https://www.nomadproject.io/): Container orchestration security audit
- [HashiCorp Cloud](https://cloud.hashicorp.com/): Cloud platform security review
- [Tekton](https://tekton.dev/): CI/CD security assessment
- [Linkerd](https://linkerd.io/): Service mesh security review
- [CoreDNS](https://coredns.io/): DNS server security audit

### Invariant Testing and Development
- [Panoptic](https://panoptic.xyz/): Protocol invariant testing
- [Curvance](https://www.curvance.com/): Smart contract invariant development
- [ParaSpace](https://para.space/): DeFi protocol testing
- [Lindylabs](https://lindylabs.net): Financial protocol testing

### Blockchain Reviews
- [Wallet Reviews](https://github.com/trailofbits/publications?tab=readme-ov-file#wallet-reviews): Comprehensive crypto wallet assessments
- [Algorand Reviews](https://github.com/trailofbits/publications?tab=readme-ov-file#algorand): Protocol and smart contract audits
- [Avalanche Reviews](https://github.com/trailofbits/publications?tab=readme-ov-file#avalanche): Platform security assessments
- [Bitcoin & Derivatives](https://github.com/trailofbits/publications?tab=readme-ov-file#bitcoin--derivatives): Protocol security reviews
- [Ethereum/EVM](https://github.com/trailofbits/publications?tab=readme-ov-file#ethereumevm): Smart contract and protocol audits
- [NervOS](https://github.com/trailofbits/publications?tab=readme-ov-file#nervos): Blockchain platform reviews
- [Starknet](https://github.com/trailofbits/publications?tab=readme-ov-file#starknet): Layer 2 solution assessments
- [Solana](https://github.com/trailofbits/publications?tab=readme-ov-file#solana): Platform and protocol reviews
- [Substrate](https://github.com/trailofbits/publications?tab=readme-ov-file#substrate): Framework security audits
- [Tendermint/Cosmos](https://github.com/trailofbits/publications?tab=readme-ov-file#tendermintcosmos): Ecosystem security reviews
- [Tezos](https://github.com/trailofbits/publications?tab=readme-ov-file#tezos): Protocol and smart contract assessments
- [TON](https://github.com/trailofbits/publications?tab=readme-ov-file#ton): Blockchain platform reviews

## Resources

- [Academic papers](https://github.com/trailofbits/publications?tab=readme-ov-file#academic-papers): Latest research publications
- [Conference presentations](https://github.com/trailofbits/publications?tab=readme-ov-file#conference-presentations): Technical talks
- [Guides and Handbooks](https://github.com/trailofbits/publications?tab=readme-ov-file#guides-and-handbooks): Security documentation
- [Datasets](https://github.com/trailofbits/publications?tab=readme-ov-file#datasets): Research datasets
- [Podcasts](https://github.com/trailofbits/publications?tab=readme-ov-file#podcasts): Security discussions
- [Public comments](https://github.com/trailofbits/publications?tab=readme-ov-file#public-comments): Industry submissions
- [Disclosures](https://github.com/trailofbits/publications?tab=readme-ov-file#disclosures): Vulnerability reports
- [Workshops](https://github.com/trailofbits/publications?tab=readme-ov-file#workshops): Training materials
- [Research reports](https://github.com/trailofbits/publications?tab=readme-ov-file#research-reports): Security analysis
- [Application security testing handbook](https://appsec.guide/): Testing guide
- [Building Secure Contracts handbook](https://secure-contracts.com/not-so-smart-contracts/cosmos/): Smart contract security
- [ZKdocs](https://www.zkdocs.com/): Zero-knowledge documentation

## Popular blockchain tools

- [Slither](https://github.com/trailofbits/publications?tab=readme-ov-file#academic-papers): Solidity analysis framework
- [Medusa](https://github.com/crytic/medusa): Blockchain fuzzing platform
- [Attacknet](https://github.com/crytic/attacknet): Security testing framework

## Popular cryptography tools

- [Decree](https://github.com/trailofbits/decree): Cryptographic protocol analysis

## Popular AI/ML tools

- [PrivacyRaven](https://github.com/trailofbits/PrivacyRaven): ML privacy testing framework
- [Fickling](https://github.com/trailofbits/fickling): Pickle security scanner

## Popular application security tools and rules

- [Semgrep rules](https://github.com/trailofbits/semgrep-rules): Security pattern matching
- [Ruzzy](https://github.com/trailofbits/ruzzy): Ruby fuzzing framework

## Other popular tools

- [WeAudit](https://marketplace.visualstudio.com/items?itemName=trailofbits.weaudit): VSCode security extension
- [SARIF Explorer](https://marketplace.visualstudio.com/items?itemName=trailofbits.sarif-explorer): Report visualization

## Recent blogs
- [Key Derivation Best Practices](https://blog.trailofbits.com/2025/01/28/best-practices-for-key-derivation/): Essential guidance for proper key derivation implementation in cryptographic applications
- [Open Source Contributions 2024](https://blog.trailofbits.com/2025/01/23/celebrating-our-2024-open-source-contributions/): Overview of 750+ merged pull requests improving security across 80 open-source projects
- [RubyGems.org Security Assessment](https://blog.trailofbits.com/2024/12/11/auditing-the-ruby-ecosystems-central-package-repository/): Comprehensive security audit findings from reviewing Ruby's critical package infrastructure
- [Advanced Semgrep Rules](https://blog.trailofbits.com/2024/12/09/35-more-semgrep-rules-infrastructure-supply-chain-and-ruby/): New collection of 115 public rules for infrastructure and supply chain security
- [AI for Solidity Development](https://blog.trailofbits.com/2024/11/19/evaluating-solidity-support-in-ai-coding-assistants/): Detailed evaluation of AI coding assistants for Solidity development
- [PyPI Security Attestations](https://blog.trailofbits.com/2024/11/14/attestations-a-new-generation-of-signatures-on-pypi/): Implementation details of new digital attestation system for Python packages
- [Filecoin Vulnerability Analysis](https://blog.trailofbits.com/2024/11/13/killing-filecoin-nodes/): Technical breakdown of critical node vulnerability discovery in Filecoin network
- [Barcode Security Research](https://blog.trailofbits.com/2024/10/31/fuzzing-between-the-lines-in-popular-barcode-software/): Fuzzing research revealing critical vulnerabilities in ZBar library
- [Linux Memory Protection](https://blog.trailofbits.com/2024/10/25/a-deep-dive-into-linuxs-new-mseal-syscall/): Analysis of new mseal syscall memory protection features
- [AWS Enclave Security](https://blog.trailofbits.com/2024/09/24/notes-on-aws-nitro-enclaves-attack-surface/): Comprehensive security guidance for AWS Nitro Enclaves deployments

## Rust Libraries

- [cargo-unmaintained](https://github.com/trailofbits/cargo-unmaintained): Identifies unmaintained Rust packages
- [dylint](https://github.com/trailofbits/dylint): Dynamic Rust linting tool
- [necessist](https://github.com/trailofbits/necessist): Test improvement tool
- [siderophile](https://github.com/trailofbits/siderophile): Finds unsafe Rust functions
- [test-fuzz](https://github.com/trailofbits/test-fuzz): AFL.rs front-end
- [weggli-native](https://github.com/trailofbits/weggli-native): C API for weggli
- [reverie](https://github.com/trailofbits/reverie): MPC-in-the-head NIZKPoK implementation
- [mcircuit](https://github.com/trailofbits/mcircuit): Boolean & arithmetic circuits library

## Go Libraries

- [go-fuzz-utils](https://github.com/trailofbits/go-fuzz-utils): Go fuzzing helper package
- [on-edge](https://github.com/trailofbits/on-edge): Defer/Panic/Recover pattern detector
- [not-going-anywhere](https://github.com/trailofbits/not-going-anywhere): Vulnerable Go programs
- [go-mutexasserts](https://github.com/trailofbits/go-mutexasserts): Mutex verification library

## Symbolic Execution

- [Manticore](https://github.com/trailofbits/manticore): Symbolic execution for binaries and smart contracts
- [ManticoreUI](https://github.com/trailofbits/ManticoreUI): GUI for Manticore
- [ManticoreUI-Ghidra](https://github.com/trailofbits/ManticoreUI-Ghidra): Ghidra integration for Manticore
- [sandshrew](https://github.com/trailofbits/sandshrew): Cryptographic verification tool
- [maat](https://github.com/trailofbits/maat): Dynamic symbolic execution framework

## C++ Tools

- [cxx-common](https://github.com/trailofbits/cxx-common): Shared C++ dependencies
- [gap](https://github.com/lifting-bits/gap): LLVM-MLIR bridge
- [sqlite_wrapper](https://github.com/trailofbits/sqlite_wrapper): SQLite C++ wrapper
- [pasta](https://github.com/trailofbits/pasta): Clang toolchain abstraction
- [vast](https://github.com/trailofbits/vast): Program analysis library
- [constexpr-everything](https://github.com/trailofbits/constexpr-everything): Constexpr code rewriter
- [RaceSanitizer](https://github.com/trailofbits/RaceSanitizer): Data race detector

## Cryptography

- [mpc-learning](https://github.com/trailofbits/mpc-learning): Multi-party computation for ML
- [indurative](https://github.com/trailofbits/indurative): Authenticated data structures
- [zkdocs](https://github.com/trailofbits/zkdocs): Zero-knowledge proof documentation
- [sholva](https://github.com/trailofbits/sholva): Program execution ZK proofs
- [circomspect](https://github.com/trailofbits/circomspect): Circom analyzer and linter

## Experimental Tools

- [magnifier](https://github.com/trailofbits/magnifier): Reverse engineering UI
- [umberto](https://github.com/trailofbits/umberto): Structured data mutation
- [honeybee](https://github.com/trailofbits/honeybee): Intel Processor Trace tools
- [sinter](https://github.com/trailofbits/sinter): macOS security agent
- [binrec-tob](https://github.com/trailofbits/binrec-tob): Binary lifter
- [microx](https://github.com/lifting-bits/microx): Instruction execution framework
- [anselm](https://github.com/trailofbits/anselm): Function behavior pattern detector
- [essence](https://github.com/trailofbits/essence): LLVM function extractor

## Fuzzing Tools

- [zfuzz](https://github.com/trailofbits/zfuzz): Snapshot fuzzer
- [grr](https://github.com/lifting-bits/grr): Binary translator for fuzzing
- [ceo](https://github.com/trailofbits/ceo): Vulnerability discovery guide
- [sienna-locomotive](https://github.com/trailofbits/sienna-locomotive): User-friendly fuzzer
- [krf](https://github.com/trailofbits/krf): Kernel fault injection
- [deepstate](https://github.com/trailofbits/deepstate): Unified fuzzing framework
- [protofuzz](https://github.com/trailofbits/protofuzz): Protocol Buffers fuzzer
- [mishegos](https://github.com/trailofbits/Mishegos): x86 decoder fuzzer

## Challenges & Data Sets

- [cb-multios](https://github.com/trailofbits/cb-multios): DARPA CGC Challenge Binaries
- [AnghaBench](https://github.com/lifting-bits/AnghaBench): C program repository
- [pegoat](https://github.com/trailofbits/pegoat): Windows security test binaries
- [ctf-challenges](https://github.com/trailofbits/ctf-challenges): CTF challenge collection

## Parsers and Analysis

- [graphtage](https://github.com/trailofbits/graphtage): Tree-like structure comparison
- [polyfile](https://github.com/trailofbits/polyfile): File format analyzer
- [polytracker](https://github.com/trailofbits/polytracker): Data flow analyzer

## Windows Utilities

- [uthenticode](https://github.com/trailofbits/uthenticode): Authenticode verifier
- [winchecksec](https://github.com/trailofbits/winchecksec): PE security feature detector
- [pe-parse](https://github.com/trailofbits/pe-parse): PE file parser
- [RpcInvestigator](https://github.com/trailofbits/RpcInvestigator): RPC endpoint analyzer
- [windows-ctl](https://github.com/trailofbits/windows-ctl): Certificate Trust List tools
- [windows-acl](https://github.com/trailofbits/windows-acl): ACL operations library


## Core Tools

- [Slither](https://github.com/crytic/slither): Solidity static analysis framework
- [Manticore](https://github.com/trailofbits/manticore): Symbolic execution platform for analysis of smart contracts and binaries
- [Algo VPN](https://github.com/trailofbits/algo): Simplified personal VPN server setup
- [DeepState](https://github.com/trailofbits/deepstate): Unified testing framework for C/C++
- [Remill](https://github.com/lifting-bits/remill): Machine code to LLVM bitcode lifter
- [PrivacyRaven](https://github.com/trailofbits/PrivacyRaven): Privacy testing for deep learning systems

## eBPF Tools

- [linuxevents](https://github.com/trailofbits/linuxevents): Linux event monitoring without kernel headers
- [ebpfpub](https://github.com/trailofbits/ebpfpub): System call monitoring across kernel versions
- [ebpf-common](https://github.com/trailofbits/ebpf-common): eBPF code generation utilities
- [btfparse](https://github.com/trailofbits/btfparse): Linux kernel BTF data parser
- [ebpfault](https://github.com/trailofbits/ebpfault): System-wide fault injector
- [ebpf-verifier](https://github.com/trailofbits/ebpf-verifier): External eBPF program verifier

## Binary Analysis

- [McSema](https://github.com/lifting-bits/mcsema): Binary to LLVM lifter
- [Anvill](https://github.com/lifting-bits/anvill): Machine code lifting primitives
- [VMill](https://github.com/lifting-bits/vmill): Snapshot-based process emulator
- [Rellic](https://github.com/trailofbits/rellic): LLVM to C decompiler
- [Codex Decompiler](https://github.com/trailofbits/codex-decompiler): AI-assisted Ghidra decompiler
- [blight](https://github.com/trailofbits/blight): Build tool instrumentation framework

## Ethereum Security

- [eth-security-toolbox](https://github.com/trailofbits/eth-security-toolbox): Docker toolbox for Ethereum security tools
- [Echidna](https://github.com/crytic/echidna): Smart contract fuzzer
- [Etheno](https://github.com/crytic/etheno): Ethereum testing tool
- [Crytic-compile](https://github.com/crytic/crytic-compile): Smart contract compilation
- [Building Secure Contracts](https://github.com/crytic/building-secure-contracts): Smart contract security guide
- [Rattle](https://github.com/crytic/rattle): EVM binary analyzer
- [pyevmasm](https://github.com/crytic/pyevmasm): EVM assembler/disassembler

## Python Libraries

- [cvedb](https://github.com/trailofbits/cvedb): CVE database library and utility
- [mimid](https://github.com/trailofbits/mimid): Standalone Mimid algorithm implementation
- [abi3audit](https://github.com/trailofbits/abi3audit): Python extension ABI violation scanner
- [fickling](https://github.com/trailofbits/fickling): Python pickle decompiler and analyzer

## SIEVE Tools

- [sv_circuit](https://github.com/trailofbits/sv_circuit): DARPA SIEVE project circuit compositor
- [clash-silicon-tinytapeout](https://github.com/trailofbits/clash-silicon-tinytapeout): Synthesized CPU in Clash
- [verilog_tools](https://github.com/trailofbits/verilog_tools): Yosys wrappers for circuit compilation
- [mcircuit](https://github.com/trailofbits/mcircuit): Boolean & arithmetic circuits library

## Guides and Tutorials

- [CTF Field Guide](https://github.com/trailofbits/ctf): CTF competition guide
- [Not Slithering Anywhere](https://github.com/trailofbits/not-slithering-anywhere): Vulnerable Python application
- [LLVM Sanitizer Tutorial](https://github.com/trailofbits/llvm-sanitizer-tutorial): Guide to building LLVM sanitizers
- [Building Secure Contracts](https://github.com/crytic/building-secure-contracts): Smart contract security guide
- [ZKDocs](https://github.com/trailofbits/zkdocs): Zero-knowledge proof documentation
- [Awesome Ethereum Security](https://github.com/crytic/awesome-ethereum-security): Ethereum security resources

## Osquery Tools

- [osquery-logger](https://github.com/trailofbits/osquery-logger): Osquery debug listener
- [osquery-extensions](https://github.com/trailofbits/osquery-extensions): Osquery extension collection

## Semgrep and CodeQL

- [itergator](https://github.com/trailofbits/itergator): Iterator invalidation detector
- [divergent-representations](https://github.com/trailofbits/divergent-representations): Variable implementation analyzer
- [semgrep-rules](https://github.com/trailofbits/semgrep-rules): Custom Semgrep rules collection
- [codeql-queries](https://github.com/trailofbits/codeql-queries): Custom CodeQL queries

## Miscellaneous Tools

- [algo](https://github.com/trailofbits/algo): WireGuard/IPsec VPN setup tool
- [cast2gif](https://github.com/trailofbits/cast2gif): Terminal recording renderer
- [nginx-json-kss](https://github.com/trailofbits/nginx-json-kss): Nginx JSON logger
- [sixtyfour](https://github.com/trailofbits/sixtyfour): 64-bit integer experiment
- [twa](https://github.com/trailofbits/twa): Web auditor
- [wasm-tob](https://github.com/trail-of-forks/wasm-tob): WebAssembly module analyzer
- [it-depends](https://github.com/trailofbits/it-depends): Dependency graph generator

## AI/ML Tools

- [PrivacyRaven](https://github.com/trailofbits/PrivacyRaven): Privacy testing for deep learning
- [Codex-Decompiler](https://github.com/trailofbits/Codex-Decompiler): AI-assisted Ghidra decompiler

## Networking Tools

- [onesixtyone](https://github.com/trailofbits/onesixtyone): Fast SNMP scanner
- [eatmynetwork](https://github.com/trailofbits/eatmynetwork): Network sandboxing script


## Contact and socials

- [Contact Us](https://www.trailofbits.com/contact/): Security consulting
- [Blog](https://blog.trailofbits.com/): Research insights
- [Twitter](https://twitter.com/trailofbits): Updates
- [LinkedIn](https://www.linkedin.com/company/trail-of-bits): Professional network
- [GitHub](https://github.com/trailofbits/): Open source projects
- [Community Forum](https://slack.empirehacking.nyc/): Discussion platform

## Optional

- [Careers](https://www.trailofbits.com/careers/): Join our team
- [Resources](https://www.trailofbits.com/resources/): Additional materials
- [Built In Best Places](https://www.trailofbits.com/): Recognition
- [Forrester Recognition](https://www.trailofbits.com/forrester/): Industry analysis

## 2025 Blog Posts

- [Best practices for key derivation](https://blog.trailofbits.com/2025/01/28/best-practices-for-key-derivation/): By Marc Ilunga
- [Celebrating our 2024 open-source contributions](https://blog.trailofbits.com/2025/01/23/celebrating-our-2024-open-source-contributions/): While Trail of Bits is known for developing security tools like Slither, Medusa, and Fickling, our engineering efforts extend far beyond our own proje...

## 2024 Blog Posts

- [Auditing the Ruby ecosystem’s central package repository](https://blog.trailofbits.com/2024/12/11/auditing-the-ruby-ecosystems-central-package-repository/): Ruby Central hired Trail of Bits to complete a security assessment and a competitive analysis of RubyGems.org, the official package management system ...
- [35 more Semgrep rules: infrastructure, supply chain, and Ruby](https://blog.trailofbits.com/2024/12/09/35-more-semgrep-rules-infrastructure-supply-chain-and-ruby/): By Matt Schwager and Travis Peters
- [Evaluating Solidity support in AI coding assistants](https://blog.trailofbits.com/2024/11/19/evaluating-solidity-support-in-ai-coding-assistants/): By Artem Dinaburg
- [Attestations: A new generation of signatures on PyPI](https://blog.trailofbits.com/2024/11/14/attestations-a-new-generation-of-signatures-on-pypi/): For the past year, we’ve worked with the Python Package Index (PyPI) on a new security feature for the Python ecosystem: index-hosted digital attestat...
- [Killing Filecoin nodes](https://blog.trailofbits.com/2024/11/13/killing-filecoin-nodes/): By Simone Monica
- [Fuzzing between the lines in popular barcode software](https://blog.trailofbits.com/2024/10/31/fuzzing-between-the-lines-in-popular-barcode-software/): By Artur Cygan
- [A deep dive into Linux’s new mseal syscall](https://blog.trailofbits.com/2024/10/25/a-deep-dive-into-linuxs-new-mseal-syscall/): By Alan Cao
- [Auditing Gradio 5, Hugging Face’s ML GUI framework](https://blog.trailofbits.com/2024/10/10/auditing-gradio-5-hugging-faces-ml-gui-framework/): This is a joint post with the Hugging Face Gradio team; read their announcement here! You can find the full report with all of the detailed findings f...
- [Securing the software supply chain with the SLSA framework](https://blog.trailofbits.com/2024/10/01/securing-the-software-supply-chain-with-the-slsa-framework/): By Cliff Smith
- [A few notes on AWS Nitro Enclaves: Attack surface](https://blog.trailofbits.com/2024/09/24/notes-on-aws-nitro-enclaves-attack-surface/): By Paweł Płatek
- [Announcing the Trail of Bits and Semgrep partnership](https://blog.trailofbits.com/2024/09/19/announcing-the-trail-of-bits-and-semgrep-partnership/): At Trail of Bits, we aim to share and develop tools and resources used in our security assessments with the broader security community. Many clients, ...
- [Inside DEF CON: Michael Brown on how AI/ML is revolutionizing cybersecurity](https://blog.trailofbits.com/2024/09/17/inside-def-con-michael-brown-on-how-ai-ml-is-revolutionizing-cybersecurity/): At DEF CON, Michael Brown, Principal Security Engineer at Trail of Bits, sat down with Michael Novinson from Information Security Media Group (ISMG) t...
- [Friends don’t let friends reuse nonces](https://blog.trailofbits.com/2024/09/13/friends-dont-let-friends-reuse-nonces/): By Joe Doyle
- [Sanitize your C++ containers: ASan annotations step-by-step](https://blog.trailofbits.com/2024/09/10/sanitize-your-c-containers-asan-annotations-step-by-step/): By Dominik Klemba and Dominik Czarnota
- [“Unstripping” binaries: Restoring debugging information in GDB with Pwndbg](https://blog.trailofbits.com/2024/09/06/unstripping-binaries-restoring-debugging-information-in-gdb-with-pwndbg/): By Jason An
- [What would you do with that old GPU?](https://blog.trailofbits.com/2024/09/05/what-would-you-do-with-that-old-gpu/): By Artem Dinaburg and Peter Goodman
- [Provisioning cloud infrastructure the wrong way, but faster](https://blog.trailofbits.com/2024/08/27/provisioning-cloud-infrastructure-the-wrong-way-but-faster/): By Artem Dinaburg
- [“YOLO” is not a valid hash construction](https://blog.trailofbits.com/2024/08/21/yolo-is-not-a-valid-hash-construction/): By Opal Wright
- [We wrote the code, and the code won](https://blog.trailofbits.com/2024/08/15/we-wrote-the-code-and-the-code-won/): By Tjaden Hess
- [Trail of Bits Advances to AIxCC Finals](https://blog.trailofbits.com/2024/08/12/trail-of-bits-advances-to-aixcc-finals/): Trail of Bits has qualified for the final round of DARPA’s AI Cyber Challenge (AIxCC)! Our Cyber Reasoning System, Buttercup, placed in the top 7 out ...
- [Trail of Bits’ Buttercup heads to DARPA’s AIxCC](https://blog.trailofbits.com/2024/08/09/trail-of-bits-buttercup-heads-to-darpas-aixcc/): With DARPA’s AI Cyber Challenge (AIxCC) semifinal starting today at DEF CON 2024, we want to introduce Buttercup, our AIxCC submission. Buttercup is a...
- [Cloud cryptography demystified: Google Cloud Platform](https://blog.trailofbits.com/2024/08/05/cloud-cryptography-demystified-google-cloud-platform/): By Scott Arciszewski
- [Our audit of Homebrew](https://blog.trailofbits.com/2024/07/30/our-audit-of-homebrew/): By William Woodruff
- [Our crypto experts answer 10 key questions](https://blog.trailofbits.com/2024/07/25/our-crypto-experts-answer-10-key-questions/): By Justin Jacob
- [Announcing AES-GEM (AES with Galois Extended Mode)](https://blog.trailofbits.com/2024/07/12/announcing-aes-gem-aes-with-galois-extended-mode/): By Scott Arciszewski
- [Trail of Bits named a leader in cybersecurity consulting services](https://blog.trailofbits.com/2024/07/09/trail-of-bits-named-a-leader-in-cybersecurity-consulting-services/): Trail of Bits has been recognized as a leader in cybersecurity consulting services according to The Forrester Wave™: Cybersecurity Consulting Services...
- [Auditing the Ask Astro LLM Q&A app](https://blog.trailofbits.com/2024/07/05/auditing-the-ask-astro-llm-qa-app/): Today, we present the second of our open-source AI security audits: a look at security issues we found in an open-source retrieval augmented generatio...
- [Quantum is unimportant to post-quantum](https://blog.trailofbits.com/2024/07/01/quantum-is-unimportant-to-post-quantum/): By Opal Wright
- [Disarming Fiat-Shamir footguns](https://blog.trailofbits.com/2024/06/24/disarming-fiat-shamir-footguns/): By Opal Wright
- [EuroLLVM 2024 trip report](https://blog.trailofbits.com/2024/06/21/eurollvm-2024-trip-report/): By Marek Surovič and Henrich Lauko
- [Themes from Real World Crypto 2024](https://blog.trailofbits.com/2024/06/18/themes-from-real-world-crypto-2024/): In March, Trail of Bits engineers traveled to the vibrant (and only slightly chilly) city of Toronto to attend Real World Crypto 2024, a three-day eve...
- [Finding mispriced opcodes with fuzzing](https://blog.trailofbits.com/2024/06/17/finding-mispriced-opcodes-with-fuzzing/): By Max Ammann
- [Understanding Apple’s On-Device and Server Foundation Models release](https://blog.trailofbits.com/2024/06/14/understanding-apples-on-device-and-server-foundations-model-release/): By Artem Dinaburg
- [PCC: Bold step forward, not without flaws](https://blog.trailofbits.com/2024/06/14/pcc-bold-step-forward-not-without-flaws/): By Adelin Travers
- [Announcing the Burp Suite Professional chapter in the Testing Handbook](https://blog.trailofbits.com/2024/06/14/announcing-the-burp-suite-professional-chapter-in-the-testing-handbook/): By Maciej Domanski
- [Exploiting ML models with pickle file attacks: Part 2](https://blog.trailofbits.com/2024/06/11/exploiting-ml-models-with-pickle-file-attacks-part-2/): By Boyan Milanov
- [Exploiting ML models with pickle file attacks: Part 1](https://blog.trailofbits.com/2024/06/11/exploiting-ml-models-with-pickle-file-attacks-part-1/): By Boyan Milanov
- [Announcing AI/ML safety and security trainings](https://blog.trailofbits.com/2024/06/07/announcing-ai-ml-safety-and-security-trainings/): By Michael D. Brown
- [Understanding AddressSanitizer: Better memory safety for your code](https://blog.trailofbits.com/2024/05/16/understanding-addresssanitizer-better-memory-safety-for-your-code/): By Dominik Klemba and Dominik Czarnota
- [A peek into build provenance for Homebrew](https://blog.trailofbits.com/2024/05/14/a-peek-into-build-provenance-for-homebrew/): By Joe Sweeney and William Woodruff
- [Using benchmarks to speed up Echidna](https://blog.trailofbits.com/2024/05/08/using-benchmarks-to-speed-up-echidna/): By Ben Siraphob
- [The life and times of an Abstract Syntax Tree](https://blog.trailofbits.com/2024/05/02/the-life-and-times-of-an-abstract-syntax-tree/): By Francesco Bertolaccini
- [Curvance: Invariants unleashed](https://blog.trailofbits.com/2024/04/30/curvance-invariants-unleashed/): By Nat Chin
- [Announcing two new LMS libraries](https://blog.trailofbits.com/2024/04/26/announcing-two-new-lms-libraries/): By Will Song
- [5 reasons to strive for better disclosure processes](https://blog.trailofbits.com/2024/04/15/5-reasons-to-strive-for-better-disclosure-processes/): By Max Ammann
- [Introducing Ruzzy, a coverage-guided Ruby fuzzer](https://blog.trailofbits.com/2024/03/29/introducing-ruzzy-a-coverage-guided-ruby-fuzzer/): By Matt Schwager
- [Why fuzzing over formal verification?](https://blog.trailofbits.com/2024/03/22/why-fuzzing-over-formal-verification/): By Tarun Bansal, Gustavo Grieco, and Josselin Feist
- [Streamline your static analysis triage with SARIF Explorer](https://blog.trailofbits.com/2024/03/20/streamline-the-static-analysis-triage-process-with-sarif-explorer/): By Vasco Franco
- [Read code like a pro with our weAudit VSCode extension](https://blog.trailofbits.com/2024/03/19/read-code-like-a-pro-with-our-weaudit-vscode-extension/): By Filipe Casal
- [Releasing the Attacknet: A new tool for finding bugs in blockchain nodes using chaos testing](https://blog.trailofbits.com/2024/03/18/releasing-the-attacknet-a-new-tool-for-finding-bugs-in-blockchain-nodes-using-chaos-testing/): By Benjamin Samuels (@thebensams)
- [Secure your blockchain project from the start](https://blog.trailofbits.com/2024/03/13/secure-your-blockchain-project-from-the-start/): Systemic security issues in blockchain projects often appear early in development. Without an initial focus on security, projects may choose flawed ar...
- [DARPA awards $1 million to Trail of Bits for AI Cyber Challenge](https://blog.trailofbits.com/2024/03/11/darpa-awards-1-million-to-trail-of-bits-for-ai-cyber-challenge/): By Michael D. Brown
- [Out of the kernel, into the tokens](https://blog.trailofbits.com/2024/03/08/out-of-the-kernel-into-the-tokens/): By Max Ammann and Emilio López
- [Cryptographic design review of Ockam](https://blog.trailofbits.com/2024/03/05/cryptographic-design-review-of-ockam/): By Marc Ilunga, Jim Miller, Fredrik Dahlgren, and Joop van de Pol
- [Relishing new Fickling features for securing ML systems](https://blog.trailofbits.com/2024/03/04/relishing-new-fickling-features-for-securing-ml-systems/): By Suha S. Hussain
- [How we applied advanced fuzzing techniques to cURL](https://blog.trailofbits.com/2024/03/01/toward-more-effective-curl-fuzzing/): By Shaun Mirani
- [When try, try, try again leads to out-of-order execution bugs](https://blog.trailofbits.com/2024/03/01/when-try-try-try-again-leads-to-out-of-order-execution-bugs/): By Troy Sargent
- [Our response to the US Army’s RFI on developing AIBOM tools](https://blog.trailofbits.com/2024/02/28/our-response-to-the-us-armys-rfi-on-developing-aibom-tools-2/): By Michael Brown and Adelin Travers
- [Circomspect has been integrated into the Sindri CLI](https://blog.trailofbits.com/2024/02/26/circomspect-has-been-integrated-into-the-sindri-cli/): By Jim Miller
- [Continuously fuzzing Python C extensions](https://blog.trailofbits.com/2024/02/23/continuously-fuzzing-python-c-extensions/): By Matt Schwager
- [Breaking the shared key in threshold signature schemes](https://blog.trailofbits.com/2024/02/20/breaking-the-shared-key-in-threshold-signature-schemes/): By Fredrik Dahlgren
- [A few notes on AWS Nitro Enclaves: Images and attestation](https://blog.trailofbits.com/2024/02/16/a-few-notes-on-aws-nitro-enclaves-images-and-attestation/): By Paweł Płatek (GrosQuildu)
- [Cloud cryptography demystified: Amazon Web Services](https://blog.trailofbits.com/2024/02/14/cloud-cryptography-demystified-amazon-web-services/): By Scott Arciszewski
- [Why Windows can’t follow WSL symlinks](https://blog.trailofbits.com/2024/02/12/why-windows-cant-follow-wsl-symlinks/): By Yarden Shafir
- [Master fuzzing with our new Testing Handbook chapter](https://blog.trailofbits.com/2024/02/09/master-fuzzing-with-our-new-testing-handbook-chapter/): Our latest addition to the Trail of Bits Testing Handbook is a comprehensive guide to fuzzing: an essential, effective, low-effort method to find bugs...
- [Binary type inference in Ghidra](https://blog.trailofbits.com/2024/02/07/binary-type-inference-in-ghidra/): By Ian Smith
- [Improving the state of Cosmos fuzzing](https://blog.trailofbits.com/2024/02/05/improving-the-state-of-cosmos-fuzzing/): By Gustavo Grieco
- [Chaos Communication Congress (37C3) recap](https://blog.trailofbits.com/2024/02/02/chaos-communication-congress-37c3-recap/): Last month, two of our engineers attended the 37th Chaos Communication Congress (37C3) in Hamburg, joining thousands of hackers who gather each year t...
- [Introducing DIFFER, a new tool for testing and validating transformed programs](https://blog.trailofbits.com/2024/01/31/introducing-differ-a-new-tool-for-testing-and-validating-transformed-programs/): By Michael Brown
- [Enhancing trust for SGX enclaves](https://blog.trailofbits.com/2024/01/26/enhancing-trust-for-sgx-enclaves/): By Artur Cygan
- [We build X.509 chains so you don’t have to](https://blog.trailofbits.com/2024/01/25/we-build-x-509-chains-so-you-dont-have-to/): By William Woodruff
- [Celebrating our 2023 open-source contributions](https://blog.trailofbits.com/2024/01/24/celebrating-our-2023-open-source-contributions/): At Trail of Bits, we pride ourselves on making our best tools open source, such as Slither, PolyTracker, and RPC Investigator. But while this post is ...
- [Our thoughts on AIxCC’s competition format](https://blog.trailofbits.com/2024/01/18/our-thoughts-on-aixccs-competition-format/): By Michael Brown
- [30 new Semgrep rules: Ansible, Java, Kotlin, shell scripts, and more](https://blog.trailofbits.com/2024/01/17/30-new-semgrep-rules-ansible-java-kotlin-shell-scripts-and-more/): By Matt Schwager and Sam Alws
- [LeftoverLocals:  Listening to LLM responses through leaked GPU local memory](https://blog.trailofbits.com/2024/01/16/leftoverlocals-listening-to-llm-responses-through-leaked-gpu-local-memory/): By Tyler Sorensen and Heidy Khlaaf
- [Internet freedom with the Open Technology Fund](https://blog.trailofbits.com/2024/01/15/internet-freedom-with-the-open-technology-fund/): By Spencer Michaels, William Woodruff, Jeff Braswell, and Cliff Smith
- [How to introduce Semgrep to your organization](https://blog.trailofbits.com/2024/01/12/how-to-introduce-semgrep-to-your-organization/): By Maciej Domanski, Application Security Engineer
- [Securing open-source infrastructure with OSTIF](https://blog.trailofbits.com/2024/01/09/securing-open-source-infrastructure-with-ostif/): The Open Source Technology Improvement Fund (OSTIF) counters an often overlooked challenge in the open-source world: the same software projects that u...
- [Tag, you’re it: Signal tagging in Circom](https://blog.trailofbits.com/2024/01/02/tag-youre-it-signal-tagging-in-circom/): By Tjaden Hess

## 2023 Blog Posts

- [Billion times emptiness](https://blog.trailofbits.com/2023/12/29/billion-times-emptiness/): By Max Ammann
- [AI In Windows: Investigating Windows Copilot](https://blog.trailofbits.com/2023/12/27/ai-in-windows-investigating-windows-copilot/): By Yarden Shafir
- [We’ve added more content to ZKDocs](https://blog.trailofbits.com/2023/12/26/weve-added-more-content-to-zkdocs/): By Jim Miller
- [Catching OpenSSL misuse using CodeQL](https://blog.trailofbits.com/2023/12/22/catching-openssl-misuse-using-codeql/): By Damien Santiago
- [Summer interns 2023 recap](https://blog.trailofbits.com/2023/12/20/summer-associates-2023-recap/): This past summer at Trail of Bits was a season of inspiration, innovation, and growth thanks to the incredible contributions of our talented interns, ...
- [A trail of flipping bits](https://blog.trailofbits.com/2023/12/18/a-trail-of-flipping-bits/): By Joop van de Pol
- [DARPA’s AI Cyber Challenge: We’re In!](https://blog.trailofbits.com/2023/12/14/darpas-ai-cyber-challenge-were-in/): 
- [Say hello to the next chapter of the Testing Handbook!](https://blog.trailofbits.com/2023/12/11/say-hello-to-the-next-chapter-of-the-testing-handbook/): By Fredrik Dahlgren
- [Publishing Trail of Bits’ CodeQL queries](https://blog.trailofbits.com/2023/12/06/publishing-trail-of-bits-codeql-queries/): By Paweł Płatek
- [ETW internals for security research and forensics](https://blog.trailofbits.com/2023/11/22/etw-internals-for-security-research-and-forensics/): By Yarden Shafir
- [How CISA can improve OSS security](https://blog.trailofbits.com/2023/11/20/how-cisa-can-improve-oss-security/): By Jim Miller
- [Assessing the security posture of a widely used vision model: YOLOv7](https://blog.trailofbits.com/2023/11/15/assessing-the-security-posture-of-a-widely-used-vision-model-yolov7/): By Alvin Crighton, Anusha Ghosh, Suha Hussain, Heidy Khlaaf, and Jim Miller
- [Our audit of PyPI](https://blog.trailofbits.com/2023/11/14/our-audit-of-pypi/): By William Woodruff
- [Adding build provenance to Homebrew](https://blog.trailofbits.com/2023/11/06/adding-build-provenance-to-homebrew/): By William Woodruff
- [The issue with ATS in Apple’s macOS and iOS](https://blog.trailofbits.com/2023/10/30/the-issue-with-ats-in-apples-macos-and-ios/): By Will Brattain
- [Numbers turned weapons: DoS in Osmosis’ math library](https://blog.trailofbits.com/2023/10/23/numbers-turned-weapons-dos-in-osmosis-math-library/): By Sam Alws
- [Introducing invariant development as a service](https://blog.trailofbits.com/2023/10/05/introducing-invariant-development-as-a-service/): Understanding and rigorously testing system invariants are essential aspects of developing robust smart contracts. Invariants are facts about the prot...
- [Pitfalls of relying on eBPF for security monitoring (and some solutions)](https://blog.trailofbits.com/2023/09/25/pitfalls-of-relying-on-ebpf-for-security-monitoring-and-some-solutions/): By Artem Dinaburg
- [Don’t overextend your Oblivious Transfer](https://blog.trailofbits.com/2023/09/20/dont-overextend-your-oblivious-transfer/): By Joop van de Pol
- [Security flaws in an SSO plugin for Caddy](https://blog.trailofbits.com/2023/09/18/security-flaws-in-an-sso-plugin-for-caddy/): By Maciej Domanski, Travis Peters, and David Pokora
- [Holy Macroni! A recipe for progressive language enhancement](https://blog.trailofbits.com/2023/09/11/holy-macroni-a-recipe-for-progressive-language-enhancement/): By Brent Pappas
- [Secure your Apollo GraphQL server with Semgrep](https://blog.trailofbits.com/2023/08/29/secure-your-apollo-graphql-server-with-semgrep/): By Vasco Franco
- [iVerify is now an independent company!](https://blog.trailofbits.com/2023/08/28/iverify-is-now-an-independent-company/): We’re proud to announce that iVerify is now an independent company following its four-year incubation at Trail of Bits. Originally developed in-house ...
- [The Engineer’s Guide to Blockchain Finality](https://blog.trailofbits.com/2023/08/23/the-engineers-guide-to-blockchain-finality/): By Benjamin Samuels
- [Can you pass the Rekt test?](https://blog.trailofbits.com/2023/08/14/can-you-pass-the-rekt-test/): One of the biggest challenges for blockchain developers is objectively assessing their security posture and measuring how it progresses. To address th...
- [Use our suite of eBPF libraries](https://blog.trailofbits.com/2023/08/09/use-our-suite-of-ebpf-libraries/): By Artem Dinaburg
- [A mistake in the bulletproofs paper could have led to the theft of millions of dollars](https://blog.trailofbits.com/2023/08/02/a-mistake-in-the-bulletproofs-paper-could-have-led-to-the-theft-of-millions-of-dollars/): By Jim Miller
- [How AI will affect cybersecurity: What we told the CFTC](https://blog.trailofbits.com/2023/07/31/how-ai-will-affect-cybersecurity-what-we-told-the-cftc/): Dan Guido, CEO
- [The future of Clang-based tooling](https://blog.trailofbits.com/2023/07/28/the-future-of-clang-based-tooling/): By Peter Goodman
- [Announcing the Trail of Bits Testing Handbook](https://blog.trailofbits.com/2023/07/26/announcing-the-trail-of-bits-testing-handbook/): By Maciej Domanski
- [Fuzzing on-chain contracts with Echidna](https://blog.trailofbits.com/2023/07/21/fuzzing-on-chain-contracts-with-echidna/): By Guillermo Larregay and Elvis Skozdopolj
- [Trail of Bits’s Response to OSTP National Priorities for AI RFI](https://blog.trailofbits.com/2023/07/18/trail-of-bitss-response-to-ostp-national-priorities-for-ai-rfi/): By Michael Brown and Heidy Khlaaf
- [Evaluating blockchain security maturity](https://blog.trailofbits.com/2023/07/14/evaluating-blockchain-security-maturity/): By Josselin Feist, Blockchain Engineering Director
- [What we told the CFTC about blockchain threats](https://blog.trailofbits.com/2023/07/12/what-we-told-the-cftc-about-crypto-threats/): Dan Guido, CEO
- [Differential fuzz testing upgradeable smart contracts with Diffusc](https://blog.trailofbits.com/2023/07/07/differential-fuzz-testing-upgradeable-smart-contracts-with-diffusc/): On March 28, 2023, SafeMoon, a self-styled “community-focused DeFi token” on Binance Smart Chain, lost the equivalent of $8.9 million in Binance Coin ...
- [Trail of Bits’s Response to NTIA AI Accountability RFC](https://blog.trailofbits.com/2023/06/16/trail-of-bitss-response-to-ntia-ai-accountability-rfc/): By Heidy Khlaaf and Artem Dinaburg
- [Finding bugs in C code with Multi-Level IR and VAST](https://blog.trailofbits.com/2023/06/15/finding-bugs-with-mlir-and-vast/): Intermediate languages (IRs) are what reverse engineers and vulnerability researchers use to see the forest for the trees. IRs are used to view progra...
- [Trusted publishing: a new benchmark for packaging security](https://blog.trailofbits.com/2023/05/23/trusted-publishing-a-new-benchmark-for-packaging-security/): Read the official announcement on the PyPI blog as well!
- [Real World Crypto 2023 Recap](https://blog.trailofbits.com/2023/05/16/real-world-crypto-2023-recap/): Last month, hundreds of cryptographers descended upon Tokyo for the first Real World Crypto Conference in Asia. As in previous years, we dispatched a ...
- [Introducing Windows Notification Facility’s (WNF) Code Integrity](https://blog.trailofbits.com/2023/05/15/introducing-windows-notification-facilitys-wnf-code-integrity/): By Yarden Shafir, Senior Security Engineer
- [What should governments consider when getting involved with blockchain?](https://blog.trailofbits.com/2023/04/25/loose-code-sinks-nodes/): Last September, Principal Security Engineer Dr. Evan Sultanik was on a panel hosted by the Naval Postgraduate School’s Distributed Consensus: Blockcha...
- [Typos that omit security features and how to test for them](https://blog.trailofbits.com/2023/04/20/typos-that-omit-security-features-and-how-to-test-for-them/): By Dominik ‘disconnect3d’ Czarnota
- [A Winter’s Tale: Improving messages and types in GDB’s Python API](https://blog.trailofbits.com/2023/04/18/a-winters-tale-improving-types-and-messages-in-gdbs-python-api/): By Matheus Branco Borella, University of São Paulo
- [How to avoid the aCropalypse](https://blog.trailofbits.com/2023/03/30/acropalypse-polytracker-blind-spots/): By Henrik Brodin, Lead Security Engineer, Research
- [Codex (and GPT-4) can’t beat humans on smart contract audits](https://blog.trailofbits.com/2023/03/22/codex-and-gpt4-cant-beat-humans-on-smart-contract-audits/): By Artem Dinaburg, Chief Technology Officer; Josselin Feist, Principal Engineer; and Riccardo Schirone, Security Engineer
- [Circomspect has more passes!](https://blog.trailofbits.com/2023/03/21/circomspect-static-analyzer-circom-more-passes/): By Fredrik Dahlgren, Principal Security Engineer
- [We need a new way to measure AI security](https://blog.trailofbits.com/2023/03/14/ai-security-safety-audit-assurance-heidy-khlaaf-odd/): Tl;dr: Trail of Bits has launched a practice focused on machine learning and artificial intelligence, bringing together safety and security methodolog...
- [Reusable properties for Ethereum contracts](https://blog.trailofbits.com/2023/02/27/reusable-properties-ethereum-contracts-echidna/): As smart contract security constantly evolves, property-based fuzzing has become a go-to technique for developers and security engineers. This techniq...
- [Escaping well-configured VSCode extensions (for profit)](https://blog.trailofbits.com/2023/02/23/escaping-well-configured-vscode-extensions-for-profit/): By Vasco Franco
- [Escaping misconfigured VSCode extensions](https://blog.trailofbits.com/2023/02/21/vscode-extension-escape-vulnerability/): By Vasco Franco
- [Readline crime: exploiting a SUID logic bug](https://blog.trailofbits.com/2023/02/16/suid-logic-bug-linux-readline/): By roddux // Rory M
- [cURL audit: How a joke led to significant findings](https://blog.trailofbits.com/2023/02/14/curl-audit-fuzzing-libcurl-command-line-interface/): By Maciej Domanski
- [Harnessing the eBPF Verifier](https://blog.trailofbits.com/2023/01/19/ebpf-verifier-harness/): By Laura Bauman
- [Introducing RPC Investigator](https://blog.trailofbits.com/2023/01/17/rpc-investigator-microsoft-windows-remote-procedure-call/): A new tool for Windows RPC research
- [Announcing a stable release of sigstore-python](https://blog.trailofbits.com/2023/01/13/sigstore-python/): By William Woodruff
- [Keeping the wolves out of wolfSSL](https://blog.trailofbits.com/2023/01/12/wolfssl-vulnerabilities-tlspuffin-fuzzing-ssh/): By Max Ammann
- [Another prolific year of open-source contributions](https://blog.trailofbits.com/2023/01/10/open-source-contributions-2022/): By Samuel Moelius

## 2022 Blog Posts

- [How to share what you’ve learned from our audits](https://blog.trailofbits.com/2022/12/22/curl-security-audit-threat-model/): By Nick Selby
- [Fast and accurate syntax searching for C and C++](https://blog.trailofbits.com/2022/12/22/syntax-searching-c-c-clang-ast/): By Mate Kukri
- [What child is this?](https://blog.trailofbits.com/2022/12/20/process-reparenting-microsoft-windows/): A Primer on Process Reparenting in Windows
- [How I gave ManticoreUI a makeover](https://blog.trailofbits.com/2022/12/15/manitcoreui-symbolic-execution-gui/): By Calvin Fong
- [Manticore GUIs made easy](https://blog.trailofbits.com/2022/12/13/manticore-gui-plugin-binary-ninja-ghidra/): By Wong Kok Rui, National University of Singapore
- [Hybrid fuzzing: Sharpening the spikes of Echidna](https://blog.trailofbits.com/2022/12/08/hybrid-echidna-fuzzing-optik-maat/): By Tom Malcolm, University of Queensland, Australia
- [Specialized Zero-Knowledge Proof failures](https://blog.trailofbits.com/2022/11/29/specialized-zero-knowledge-proof-failures/): By Opal Wright
- [ABI compatibility in Python: How hard could it be?](https://blog.trailofbits.com/2022/11/15/python-wheels-abi-abi3audit/): TL;DR: Trail of Bits has developed abi3audit, a new Python tool for checking Python packages for CPython application binary interface (ABI) violations...
- [We’re streamers now](https://blog.trailofbits.com/2022/11/14/livestream-workshop-fuzzing-echidna-slither/): Over the years, we’ve built many high-impact tools that we use for security reviews. You might know some of them, like Slither, Echidna, Amarna, Teale...
- [Look out! Divergent representations are everywhere!](https://blog.trailofbits.com/2022/11/10/divergent-representations-variable-overflows-c-compiler/): By Andreas Kellas
- [We sign code now](https://blog.trailofbits.com/2022/11/08/sigstore-code-signing-verification-software-supply-chain/): By William Woodruff
- [Stranger Strings: An exploitable flaw in SQLite](https://blog.trailofbits.com/2022/10/25/sqlite-vulnerability-july-2022-library-api/): By Andreas Kellas
- [We do Windows now](https://blog.trailofbits.com/2022/10/18/microsoft-windows-security-research-team/): At Trail of Bits, we pride ourselves on building tools that everyone can use to help improve the security ecosystem. Given how ingrained Microsoft is ...
- [Porting the Solana eBPF JIT compiler to ARM64](https://blog.trailofbits.com/2022/10/12/solana-jit-compiler-ebpf-arm64/): By Andrew Haberlandt
- [Working on blockchains as a Trail of Bits intern](https://blog.trailofbits.com/2022/10/05/trail-of-bits-internship-blockchain-tealer/): By Vara Prasad Bandaru
- [Secure your machine learning with Semgrep](https://blog.trailofbits.com/2022/10/03/semgrep-maching-learning-static-analysis/): By Suha Hussain
- [It pays to be Circomspect](https://blog.trailofbits.com/2022/09/15/it-pays-to-be-circomspect/): By Fredrik Dahlgren, Staff Security Engineer
- [Magnifier: An Experiment with Interactive Decompilation](https://blog.trailofbits.com/2022/08/25/magnifier-an-experiment-with-interactive-decompilation/): By Alan Chang
- [Using mutants to improve Slither](https://blog.trailofbits.com/2022/08/17/using-mutants-to-improve-slither/): By Alex Groce, Northern Arizona University
- [The road to the apprenticeship](https://blog.trailofbits.com/2022/08/12/the-road-to-the-apprenticeship/): By Josselin Feist, Principal Security Engineer
- [Shedding smart contract storage with Slither](https://blog.trailofbits.com/2022/07/28/shedding-smart-contract-storage-with-slither/): By Troy Sargent, Blockchain Security Engineer
- [libmagic: The Blathering](https://blog.trailofbits.com/2022/07/01/libmagic-the-blathering/): By Evan Sultanik, Principal Security Engineer
- [A Typical Day as a Trail of Bits Engineer-Consultant](https://blog.trailofbits.com/2022/06/30/a-typical-day-as-a-trail-of-bits-engineer-consultant/): Wherever you are in the world, a typical day as a Trail of Bits Engineer-Consultant means easing into your work.
- [The Trail of Bits Hiring Process](https://blog.trailofbits.com/2022/06/28/the-trail-of-bits-hiring-process/): When engineers apply to Trail of Bits, they’re often surprised by how straightforward and streamlined our hiring process is. After years of experience...
- [Managing risk in blockchain deployments](https://blog.trailofbits.com/2022/06/24/managing-risk-in-blockchain-deployments/): Do you need a blockchain? And if so, what kind?
- [Are blockchains decentralized?](https://blog.trailofbits.com/2022/06/21/are-blockchains-decentralized/): A new Trail of Bits research report examines unintended centralities in distributed ledgers
- [Announcing the new Trail of Bits podcast](https://blog.trailofbits.com/2022/06/20/announcing-the-new-trail-of-bits-podcast/): Trail of Bits has launched a podcast. The first five-episode season is now available for download. The podcast and its RSS feed are available at trail...
- [Themes from PyCon US 2022](https://blog.trailofbits.com/2022/06/09/themes-from-pycon-us-2022/): By Adam Meily
- [Interactive decompilation with rellic-xref](https://blog.trailofbits.com/2022/05/17/interactive-decompilation-with-rellic-xref/): By Francesco Bertolaccini
- [Themes from Real World Crypto 2022](https://blog.trailofbits.com/2022/05/03/themes-from-real-world-crypto-2022/): By William Woodruff
- [Improving the state of go-fuzz](https://blog.trailofbits.com/2022/04/26/improving-the-state-of-go-fuzz/): By Christian Presa Schnell
- [Amarna: Static analysis for Cairo programs](https://blog.trailofbits.com/2022/04/20/amarna-static-analysis-for-cairo-programs/): By Filipe Casal
- [The Frozen Heart vulnerability in PlonK](https://blog.trailofbits.com/2022/04/18/the-frozen-heart-vulnerability-in-plonk/): By Jim Miller
- [The Frozen Heart vulnerability in Bulletproofs](https://blog.trailofbits.com/2022/04/15/the-frozen-heart-vulnerability-in-bulletproofs/): By Jim Miller
- [The Frozen Heart vulnerability in Girault’s proof of knowledge](https://blog.trailofbits.com/2022/04/14/the-frozen-heart-vulnerability-in-giraults-proof-of-knowledge/): By Jim Miller
- [Coordinated disclosure of vulnerabilities affecting Girault, Bulletproofs, and PlonK](https://blog.trailofbits.com/2022/04/13/part-1-coordinated-disclosure-of-vulnerabilities-affecting-girault-bulletproofs-and-plonk/): By Jim Miller
- [Towards Practical Security Optimizations for Binaries](https://blog.trailofbits.com/2022/03/25/towards-practical-security-optimizations-for-binaries/): By Michael D. Brown, Senior Security Engineer
- [Optimizing a smart contract fuzzer](https://blog.trailofbits.com/2022/03/02/optimizing-a-smart-contract-fuzzer/): By Sam Alws
- [Maat: Symbolic execution made easy](https://blog.trailofbits.com/2022/02/23/maat-symbolic-execution-made-easy/): By Boyan Milanov
- [Part 2: Improving crypto code in Rust using LLVM’s optnone](https://blog.trailofbits.com/2022/02/01/part-2-rusty-crypto/): By Henrik Brodin
- [Part 1: The life of an optimization barrier](https://blog.trailofbits.com/2022/01/26/part-1-the-life-of-an-optimization-barrier/): By Fredrik Dahlgren
- [C your data structures with rellic-headergen](https://blog.trailofbits.com/2022/01/19/c-your-data-structures-with-rellic-headergen/): By Francesco Bertolaccini
- [Finding unhandled errors using CodeQL](https://blog.trailofbits.com/2022/01/11/finding-unhandled-errors-using-codeql/): By Fredrik Dahlgren
- [Toward a Best-of-Both-Worlds Binary Disassembler](https://blog.trailofbits.com/2022/01/05/toward-a-best-of-both-worlds-binary-disassembler/): By Stefan Nagy

## 2021 Blog Posts

- [Celebrating our 2021 Open Source Contributions](https://blog.trailofbits.com/2021/12/31/celebrating-our-2021-open-source-contributions/): At Trail of Bits, we pride ourselves on making our best tools open source, such as algo, manticore, and graphtage. But while this post is about open s...
- [Disclosing Shamir’s Secret Sharing vulnerabilities and announcing ZKDocs](https://blog.trailofbits.com/2021/12/21/disclosing-shamirs-secret-sharing-vulnerabilities-and-announcing-zkdocs/): By Filipe Casal and Jim Miller
- [Detecting MISO and Opyn’s msg.value reuse vulnerability with Slither](https://blog.trailofbits.com/2021/12/16/detecting-miso-and-opyns-msg-value-reuse-vulnerability-with-slither/): By Simone Monica
- [What does your code use, and is it vulnerable? It-depends!](https://blog.trailofbits.com/2021/12/16/it-depends/): You just cloned a fresh source code repository and want to get a quick sense of its dependencies. Our tool, it-depends, can get you there.
- [MUI: Visualizing symbolic execution with Manticore and Binary Ninja](https://blog.trailofbits.com/2021/11/17/mui-visualizing-symbolic-execution-with-manticore-and-binary-ninja/): By Alan Chang, University of Oxford
- [How to choose an interesting project](https://blog.trailofbits.com/2021/11/12/how-to-choose-an-interesting-project/): By Trent Brunson, Head of Research & Engineering
Originally published on October 15, 2021
- [Motivating global stabilization](https://blog.trailofbits.com/2021/11/11/motivating-global-stabilization/): By Samuel Moelius, Staff Engineer
Originally published on October 12, 2021
- [Announcing osquery 5: Now with EndpointSecurity on macOS](https://blog.trailofbits.com/2021/11/10/announcing-osquery-5-now-with-endpointsecurity-on-macos/): By Sharvil Shah, Senior Software Engineer
Originally published on October 6, 2021
- [All your tracing are belong to BPF](https://blog.trailofbits.com/2021/11/09/all-your-tracing-are-belong-to-bpf/): By Alessandro Gario, Senior Software Engineer
Originally published August 11, 2021
- [PrivacyRaven: Implementing a proof of concept for model inversion](https://blog.trailofbits.com/2021/11/09/privacyraven-implementing-a-proof-of-concept-for-model-inversion/): By Philip Wang, Intern
Originally published August 3, 2021
- [Write Rust lints without forking Clippy](https://blog.trailofbits.com/2021/11/09/write-rust-lints-without-forking-clippy/): By Samuel Moelius, Staff Engineer
Originally published May 20, 2021
- [Discovering goroutine leaks with Semgrep](https://blog.trailofbits.com/2021/11/08/discovering-goroutine-leaks-with-semgrep/): By Alex Useche, Security Engineer
Originally published May 10, 2021
- [Solar: Context-free, interactive analysis for Solidity](https://blog.trailofbits.com/2021/04/02/solar-context-free-interactive-analysis-for-solidity/): We’re hiring for our Research + Engineering team!
- [A Year in the Life of a Compiler Fuzzing Campaign](https://blog.trailofbits.com/2021/03/23/a-year-in-the-life-of-a-compiler-fuzzing-campaign/): By Alex Groce, Northern Arizona University
- [Un-bee-lievable Performance: Fast Coverage-guided Fuzzing with Honeybee and Intel Processor Trace](https://blog.trailofbits.com/2021/03/19/un-bee-lievable-performance-fast-coverage-guided-fuzzing-with-honeybee-and-intel-processor-trace/): By Allison Husain, UC Berkeley
- [Never a dill moment: Exploiting machine learning pickle files](https://blog.trailofbits.com/2021/03/15/never-a-dill-moment-exploiting-machine-learning-pickle-files/): By Evan Sultanik
- [The Tao of Continuous Integration](https://blog.trailofbits.com/2021/02/26/the-tao-of-continuous-integration/): By Paul Kehrer
- [Serving up zero-knowledge proofs](https://blog.trailofbits.com/2021/02/19/serving-up-zero-knowledge-proofs/): By Jim Miller, Senior Cryptography Analyst
- [Confessions of a smart contract paper reviewer](https://blog.trailofbits.com/2021/02/05/confessions-of-a-smart-contract-paper-reviewer/): If you’re thinking of writing a paper describing an exciting novel approach to smart contract analysis and want to know what reviewers will be looking...
- [PDF is Broken: a justCTF Challenge](https://blog.trailofbits.com/2021/02/02/pdf-is-broken-a-justctf-challenge/): Trail of Bits sponsored the recent justCTF competition, and our engineers helped craft several of the challenges, including D0cker, Go-fs, Pinata, Ora...

## 2020 Blog Posts

- [Breaking Aave Upgradeability](https://blog.trailofbits.com/2020/12/16/breaking-aave-upgradeability/): On December 3rd, Aave deployed version 2 of their codebase. While we were not hired to look at the code, we briefly reviewed it the following day. We ...
- [Reverie: An optimized zero-knowledge proof system](https://blog.trailofbits.com/2020/12/14/reverie-an-optimized-zero-knowledge-proof-system/): Zero-knowledge proofs, once a theoretical curiosity, have recently seen widespread deployment in blockchain systems such as Zcash and Monero. However,...
- [High-fidelity build instrumentation with blight](https://blog.trailofbits.com/2020/11/25/high-fidelity-build-instrumentation-with-blight/): TL;DR: We’re open-sourcing a new framework, blight, for painlessly wrapping and instrumenting C and C++ build tools. We’re already using it on our res...
- [Smart (and simple) ways to prevent symlink attacks in Go](https://blog.trailofbits.com/2020/11/24/smart-and-simple-ways-to-prevent-symlink-attacks-in-go/): After writing Go for years, many of us have learned the error-checking pattern down to our bones: “Does this function return an error? Ope, better mak...
- [Good idea, bad design: How the Diamond standard falls short](https://blog.trailofbits.com/2020/10/30/good-idea-bad-design-how-the-diamond-standard-falls-short/): TL;DR: We audited an implementation of the Diamond standard proposal for contract upgradeability and can’t recommend it in its current form—but see ou...
- [Efficient audits with machine learning and Slither-simil](https://blog.trailofbits.com/2020/10/23/efficient-audits-with-machine-learning-and-slither-simil/): by Sina Pilehchiha, Concordia University
- [Let’s build a high-performance fuzzer with GPUs!](https://blog.trailofbits.com/2020/10/22/lets-build-a-high-performance-fuzzer-with-gpus/): by Ryan Eberhardt, Stanford University
- [Osquery: Using D-Bus to query systemd data](https://blog.trailofbits.com/2020/10/14/osquery-using-d-bus-to-query-systemd-data/): by Rachel Cipkins, Stevens Institute of Technology
- [Detecting Iterator Invalidation with CodeQL](https://blog.trailofbits.com/2020/10/09/detecting-iterator-invalidation-with-codeql/): by Kevin Higgs, Montgomery Blair High School
- [PrivacyRaven Has Left the Nest](https://blog.trailofbits.com/2020/10/08/privacyraven-has-left-the-nest/): By Suha S. Hussain, Georgia Tech
- [Graphtage: A New Semantic Diffing Tool](https://blog.trailofbits.com/2020/08/28/graphtage/): Graphtage is a command line utility and underlying library for semantically comparing and merging tree-like structures such as JSON, JSON5, XML, HTML,...
- [Using Echidna to test a smart contract library](https://blog.trailofbits.com/2020/08/17/using-echidna-to-test-a-smart-contract-library/): In this post, we’ll show you how to test your smart contracts with the Echidna fuzzer. In particular, you’ll see how to:
- [Sinter: New user-mode security enforcement for macOS](https://blog.trailofbits.com/2020/08/12/sinter-new-user-mode-security-enforcement-for-macos/): TL;DR: Sinter is the first available open-source endpoint protection agent written entirely in Swift, with support for Apple’s new EndpointSecurity AP...
- [Accidentally stepping on a DeFi lego](https://blog.trailofbits.com/2020/08/05/accidentally-stepping-on-a-defi-lego/): The initial release of yVault contained logic for computing the price of yUSDC that could be manipulated by an attacker to drain most (if not all) of ...
- [Contract verification made easier](https://blog.trailofbits.com/2020/07/12/new-manticore-verifier-for-smart-contracts/): Smart contract authors can now express security properties in the same language they use to write their code (Solidity) and our new tool, manticore-ve...
- [Advocating for change](https://blog.trailofbits.com/2020/06/17/advocating-for-change/): As a company, we believe Black lives matter. In the face of continued police brutality, racial disparities in law enforcement, and limited accountabil...
- [Upgradeable contracts made safer with Crytic](https://blog.trailofbits.com/2020/06/12/upgradeable-contracts-made-safer-with-crytic/): Upgradeable contracts are not as safe as you think. Architectures for upgradeability can be flawed, locking contracts, losing data, or sabotaging your...
- [ECDSA: Handle with Care](https://blog.trailofbits.com/2020/06/11/ecdsa-handle-with-care/): The elliptic curve digital signature algorithm (ECDSA) is a common digital signature scheme that we see in many of our code reviews. It has some desir...
- [How to check if a mutex is locked in Go](https://blog.trailofbits.com/2020/06/09/how-to-check-if-a-mutex-is-locked-in-go/): TL;DR: Can we check if a mutex is locked in Go? Yes, but not with a mutex API. Here’s a solution for use in debug builds.
- [Breaking the Solidity Compiler with a Fuzzer](https://blog.trailofbits.com/2020/06/05/breaking-the-solidity-compiler-with-a-fuzzer/): Over the last few months, we’ve been fuzzing solc, the standard Solidity smart contract compiler, and we’ve racked up almost 20 (now mostly fixed) new...
- [Detecting Bad OpenSSL Usage](https://blog.trailofbits.com/2020/05/29/detecting-bad-openssl-usage/): by William Wang, UCLA
- [Verifying Windows binaries, without Windows](https://blog.trailofbits.com/2020/05/27/verifying-windows-binaries-without-windows/): TL;DR: We’ve open-sourced a new library, μthenticode, for verifying Authenticode signatures on Windows PE binaries without a Windows machine. We’ve al...
- [Emerging Talent: Winternship 2020 Highlights](https://blog.trailofbits.com/2020/05/22/emerging-talent-winternship-2020-highlights/): The Trail of Bits Winternship is our winter internship program where we invite 10-15 students to join us over the winter break for a short project tha...
- [Reinventing Vulnerability Disclosure using Zero-knowledge Proofs](https://blog.trailofbits.com/2020/05/21/reinventing-vulnerability-disclosure-using-zero-knowledge-proofs/): We, along with our partner Matthew Green at Johns Hopkins University, are using zero-knowledge (ZK) proofs to establish a trusted landscape in which t...
- [Bug Hunting with Crytic](https://blog.trailofbits.com/2020/05/15/bug-hunting-with-crytic/): Crytic, our Github app for discovering smart contract flaws, is kind of a big deal: It detects security issues without human intervention, providing c...
- [Announcing the 1st International Workshop on Smart Contract Analysis](https://blog.trailofbits.com/2020/04/23/announcing-the-1st-international-workshop-on-smart-contract-analysis/): At Trail of Bits we do more than just security audits: We also push the boundaries of research in vulnerability detection tools, regularly present our...
- [Revisiting 2000 cuts using Binary Ninja’s new decompiler](https://blog.trailofbits.com/2020/04/17/revisiting-2000-cuts-using-binary-ninjas-new-decompiler/): It’s been four years since my blog post “2000 cuts with Binary Ninja.” Back then, Binary Ninja was in a private beta and the blog post response surpri...
- [Announcing our first virtual Empire Hacking](https://blog.trailofbits.com/2020/04/07/announcing-our-first-virtual-empire-hacking/): At Trail of Bits, we’ve all been working remotely due to COVID-19. But the next Empire Hacking event will go on, via video conference!
- [An Echidna for all Seasons](https://blog.trailofbits.com/2020/03/30/an-echidna-for-all-seasons/): TL;DR: We have improved Echidna with tons of new features and enhancements since it was released—and there’s more to come.
- [Announcing the Zeek Agent](https://blog.trailofbits.com/2020/03/23/announcing-the-zeek-agent/): The Zeek Network Security Monitor provides a powerful open-source platform for network traffic analysis. However, from its network vantage point, Zeek...
- [Financial Cryptography 2020 Recap](https://blog.trailofbits.com/2020/03/18/financial-cryptography-2020-recap/): A few weeks ago, we went to the 24th Financial Cryptography (FC) conference and the Workshop on Trusted Smart Contracts (WTSC), where we presented our...
- [Real-time file monitoring on Windows with osquery](https://blog.trailofbits.com/2020/03/16/real-time-file-monitoring-on-windows-with-osquery/): TL;DR: Trail of Bits has developed ntfs_journal_events, a new event-based osquery table for Windows that enables real-time file change monitoring. You...
- [Our Full Report on the Voatz Mobile Voting Platform](https://blog.trailofbits.com/2020/03/13/our-full-report-on-the-voatz-mobile-voting-platform/): Voatz allows voters to cast their ballots from any geographic location on supported mobile devices. Its mobile voting platform is under increasing pub...
- [Manticore discovers the ENS bug](https://blog.trailofbits.com/2020/03/03/manticore-discovers-the-ens-bug/): The Ethereum Name Service (ENS) contract recently suffered from a critical bug that prompted a security advisory and a migration to a new contract (CV...
- [Symbolically Executing WebAssembly in Manticore](https://blog.trailofbits.com/2020/01/31/symbolically-executing-webassembly-in-manticore/): With the release of Manticore 0.3.3, we’re proud to announce support for symbolically executing WebAssembly (WASM) binaries. WASM is a newly standardi...
- [Themes from Real World Crypto 2020](https://blog.trailofbits.com/2020/01/23/themes-from-real-world-crypto-2020/): Over 642 brilliant cryptographic minds gathered for Real World Crypto 2020, an annual conference that brings together cryptographic researchers with d...
- [Exploiting the Windows CryptoAPI Vulnerability](https://blog.trailofbits.com/2020/01/16/exploiting-the-windows-cryptoapi-vulnerability/): On Tuesday, the NSA announced they had found a critical vulnerability in the certificate validation functionality on Windows 10 and Windows Server 201...

## 2019 Blog Posts

- [Mainnet360: joint economic and security reviews with Prysm Group](https://blog.trailofbits.com/2019/12/09/introducing-mainnet360-a-joint-economic-and-security-assessment-with-prysm-group/): On Monday, October 28th at the Crypto Economics Security Conference, Trail of Bits announced a new joint offering with Prysm Group: Mainnet360. Carefu...
- [64 Bits ought to be enough for anybody!](https://blog.trailofbits.com/2019/11/27/64-bits-ought-to-be-enough-for-anybody/): How quickly can we use brute force to guess a 64-bit number? The short answer is, it all depends on what resources are available. So we’re going to ex...
- [Introducing iVerify, the security toolkit for iPhone users](https://blog.trailofbits.com/2019/11/14/introducing-iverify-the-security-toolkit-for-iphone-users/): “If privacy matters, it should matter to the phone your life is on.” So says Apple in their recent ads about Privacy on the iPhone and controlling the...
- [Announcing the Crytic $10k Research Prize](https://blog.trailofbits.com/2019/11/13/announcing-the-crytic-10k-research-prize/): At Trail of Bits, we make a significant effort to stay up to date with the academic world. We frequently evaluate our work through peer-reviewed confe...
- [Everything You Ever Wanted To Know About Test-Case Reduction, But Didn’t Know to Ask](https://blog.trailofbits.com/2019/11/11/test-case-reduction/): Imagine reducing the amount of code and time needed to test software, while at the same time increasing the efficacy of your tests and making your deb...
- [Security assessment techniques for Go projects](https://blog.trailofbits.com/2019/11/07/attacking-go-vr-ttps/): The Trail of Bits Assurance practice has received an influx of Go projects, following the success of our Kubernetes assessment this summer. As a resul...
- [Two New Tools that Tame the Treachery of Files](https://blog.trailofbits.com/2019/11/01/two-new-tools-that-tame-the-treachery-of-files/): Parsing is hard, even when a file format is well specified. But when the specification is ambiguous, it leads to unintended and strange parser and int...
- [Destroying x86_64 instruction decoders with differential fuzzing](https://blog.trailofbits.com/2019/10/31/destroying-x86_64-instruction-decoders-with-differential-fuzzing/): TL;DR: x86_64 decoding is hard, and the number and variety of implementations available for it makes it uniquely suited to differential fuzzing. We’re...
- [How safe browsing fails to protect user privacy](https://blog.trailofbits.com/2019/10/30/how-safe-browsing-fails-to-protect-user-privacy/): Recently, security researchers discovered that Apple was sending safe browsing data to Tencent for all Chinese users. This revelation has brought the ...
- [Grace Hopper Celebration (GHC) 2019 Recap](https://blog.trailofbits.com/2019/10/29/grace-hopper-celebration-2019/): by Rachel Cipkins, Stevens Institute of Technology, Hoboken, NJ
- [Formal Analysis of the CBC Casper Consensus Algorithm with TLA+](https://blog.trailofbits.com/2019/10/25/formal-analysis-of-the-cbc-casper-consensus-algorithm-with-tla/): by Anne Ouyang, Piedmont Hills High School, San Jose, CA
- [Watch Your Language: Our First Vyper Audit](https://blog.trailofbits.com/2019/10/24/watch-your-language-our-first-vyper-audit/): A lot of companies are working on Ethereum smart contracts, yet writing secure contracts remains a difficult task. You still have to avoid common pitf...
- [Multi-Party Computation on Machine Learning](https://blog.trailofbits.com/2019/10/04/multi-party-computation-on-machine-learning/): During my internship this summer, I built a multi-party computation (MPC) tool that implements a 3-party computation protocol for perceptron and suppo...
- [TSC Frequency For All: Better Profiling and Benchmarking](https://blog.trailofbits.com/2019/10/03/tsc-frequency-for-all-better-profiling-and-benchmarking/): Have you ever tried using LLVM’s X-Ray profiling tools to make some flame graphs, but gotten obscure errors like:
- [Tethered jailbreaks are back](https://blog.trailofbits.com/2019/09/27/tethered-jailbreaks-are-back/): Earlier today, a new iPhone Boot ROM exploit, checkm8 (or Apollo or Moonshine), was published on GitHub by axi0mX, affecting the iPhone 4S through the...
- [QueryCon 2019: A Turning Point for osquery](https://blog.trailofbits.com/2019/09/20/querycon-2019-a-turning-point-for-osquery/): Has it really been 3 months since Trail of Bits hosted QueryCon? We’ve had such a busy and productive summer that we nearly forgot to go back and refl...
- [Crypto 2019 Takeaways](https://blog.trailofbits.com/2019/09/11/crypto-2019-takeaways/): This year’s IACR Crypto conference was an excellent blend of far-out theory and down-to-earth pragmatism. A major theme throughout the conference was ...
- [DeepState Now Supports Ensemble Fuzzing](https://blog.trailofbits.com/2019/09/03/deepstate-now-supports-ensemble-fuzzing/): by Alan Cao, Francis Lewis High School, Queens, NY
- [Rewriting Functions in Compiled Binaries](https://blog.trailofbits.com/2019/09/02/rewriting-functions-in-compiled-binaries/): by Aditi Gupta, Carnegie Mellon University

As a summer intern at Trail of Bits, I’ve been working on building Fennec, a tool to automatically replace...
- [Binary symbolic execution with KLEE-Native](https://blog.trailofbits.com/2019/08/30/binary-symbolic-execution-with-klee-native/): by Sai Vegasena, New York University, and Peter Goodman, Senior Security Engineer
- [Reverse Taint Analysis Using Binary Ninja](https://blog.trailofbits.com/2019/08/29/reverse-taint-analysis-using-binary-ninja/): by Henry Wildermuth, Horace Mann High School
- [Wrapper’s Delight](https://blog.trailofbits.com/2019/08/26/wrappers-delight/): by Patrick Palka, University of Illinois at Chicago
- [A Day in the Life of Alessandro Gario, Senior Security Engineer](https://blog.trailofbits.com/2019/08/09/a-day-in-the-life-of-alessandro-gario-senior-security-engineer/): People interested in joining Trail of Bits often ask us what it’s like to work on the Engineering Services team. We felt that the best answer would be...
- [246 Findings From our Smart Contract Audits: An Executive Summary](https://blog.trailofbits.com/2019/08/08/246-findings-from-our-smart-contract-audits-an-executive-summary/): Until now, smart contract security researchers (and developers) have been frustrated by limited information about the actual flaws that survive seriou...
- [From The Depths Of Counterfeit Smartphones](https://blog.trailofbits.com/2019/08/07/from-the-depths-of-counterfeit-smartphones/): In an age of online second-hand retailers, marketplace exchanges, and third-party refurb shops, it’s easier than ever to save hundreds of dollars when...
- [Better Encrypted Group Chat](https://blog.trailofbits.com/2019/08/06/better-encrypted-group-chat/): Broadly, an end-to-end encrypted messaging protocol is one that ensures that only the participants in a conversation, and no intermediate servers, rou...
- [Crytic: Continuous Assurance for Smart Contracts](https://blog.trailofbits.com/2019/08/02/crytic-continuous-assurance-for-smart-contracts/): Note: This blog has been reposted from Truffle Suite’s blog.
- [Understanding Docker container escapes](https://blog.trailofbits.com/2019/07/19/understanding-docker-container-escapes/): Trail of Bits recently completed a security assessment of Kubernetes, including its interaction with Docker. Felix Wilhelm’s recent tweet of a Proof o...
- [Trail of Bits Named in Forrester Wave as a Leader in Midsize Cybersecurity Consulting Services](https://blog.trailofbits.com/2019/07/16/trail-of-bits-named-in-forrester-wave-as-a-leader-in-midsize-cybersecurity-consulting-services/): Trail of Bits was among the select companies that Forrester invited to participate in its recent report, The Forrester Wave™: Midsize Cybersecurity Co...
- [On LibraBFT’s use of broadcasts](https://blog.trailofbits.com/2019/07/12/librabft/): LibraBFT is the Byzantine Fault Tolerant (BFT) consensus algorithm used by the recently released Libra cryptocurrency. LibraBFT is based on another BF...
- [Seriously, stop using RSA](https://blog.trailofbits.com/2019/07/08/fuck-rsa/): Here at Trail of Bits we review a lot of code. From major open source projects to exciting new proprietary software, we’ve seen it all. But one common...
- [Avoiding Smart Contract “Gridlock” with Slither](https://blog.trailofbits.com/2019/07/03/avoiding-smart-contract-gridlock-with-slither/): A denial-of-service (DoS) vulnerability, dubbed ‘Gridlock,’ was publicly reported on July 1st in one of Edgeware’s smart contracts deployed on Ethereu...
- [State of the Art Proof-of-Work: RandomX](https://blog.trailofbits.com/2019/07/02/state/): RandomX is a new ASIC and GPU-resistant proof-of-work (PoW) algorithm originally developed for Monero, but potentially useful in any blockchain using ...
- [Siderophile: Expose your Crate’s Unsafety](https://blog.trailofbits.com/2019/07/01/siderophile-expose-your-crates-unsafety/): Today we released a tool, siderophile, that helps Rust developers find fuzzing targets in their codebases.
- [Use constexpr for faster, smaller, and safer code](https://blog.trailofbits.com/2019/06/27/use-constexpr-for-faster-smaller-and-safer-code/): With the release of C++14, the standards committee strengthened one of the coolest modern features of C++: constexpr. Now, C++ developers can write co...
- [Panicking the right way in Go](https://blog.trailofbits.com/2019/06/26/panicking-the-right-way-in-go/): A common Go idiom is to (1) panic, (2) recover from the panic in a deferred function, and (3) continue on. In general, this is okay, so long there are...
- [Creating an LLVM Sanitizer from Hopes and Dreams](https://blog.trailofbits.com/2019/06/25/creating-an-llvm-sanitizer-from-hopes-and-dreams/): Each year, Trail of Bits runs a month-long winter internship aka “winternship” program. This year we were happy to host 4 winterns who contributed to ...
- [Getting 2FA Right in 2019](https://blog.trailofbits.com/2019/06/20/getting-2fa-right-in-2019/): Since March, Trail of Bits has been working with the Python Software Foundation to add two-factor authentication (2FA) to Warehouse, the codebase that...
- [Trail of Bits @ ICSE 2019 – Recap](https://blog.trailofbits.com/2019/06/19/trail-of-bits-icse-2019-recap/): Three weeks ago, we presented our work on Slither at WETSEB, an ICSE workshop. ICSE is a top-tier academic conference, focused on software engineering...
- [Why you should go to QueryCon this week](https://blog.trailofbits.com/2019/06/18/why-you-should-go-to-querycon-this-week/): QueryCon takes place this week at the Convene Conference Center in Downtown Manhattan, Thursday June 20th- Friday June 21st. If you don’t have a ticke...
- [Leaves of Hash](https://blog.trailofbits.com/2019/06/17/leaves-of-hash/): Trail of Bits has released Indurative, a cryptographic library that enables authentication of a wide variety of data structures without requiring user...
- [Announcing Manticore 0.3.0](https://blog.trailofbits.com/2019/06/07/announcing-manticore-0-3-0/): Earlier this week, Manticore leapt forward to version 0.3.0. Advances for our symbolic execution engine now include: “fast forwarding” through concret...
- [Using osquery for remote forensics](https://blog.trailofbits.com/2019/05/31/using-osquery-for-remote-forensics/): System administrators use osquery for endpoint telemetry and daily monitoring. Security threat hunters use it to find indicators of compromise on thei...
- [Fuzzing Unit Tests with DeepState and Eclipser](https://blog.trailofbits.com/2019/05/31/fuzzing-unit-tests-with-deepstate-and-eclipser/): If unit tests are important to you, there’s now another reason to use DeepState, our Google-Test-like property-based testing tool for C and C++. It’s ...
- [Announcing Automated Reverse Engineering Trainings](https://blog.trailofbits.com/2019/05/30/announcing-automated-reverse-engineering-trainings/): Trail of Bits is excited to announce new training offerings for automated reverse engineering with Binary Ninja.
- [Slither: The Leading Static Analyzer for Smart Contracts](https://blog.trailofbits.com/2019/05/27/slither-the-leading-static-analyzer-for-smart-contracts/): We have published an academic paper on Slither, our static analysis framework for smart contracts, in the International Workshop on Emerging Trends in...
- [Announcing the community-oriented osquery fork, osql](https://blog.trailofbits.com/2019/04/18/announcing-the-community-oriented-osquery-fork-osql/): For months, Facebook has been heavily refactoring the entire osquery codebase, migrating osquery away from standard development tools like CMake and i...
- [Announcing QueryCon 2019](https://blog.trailofbits.com/2019/04/09/announcing-querycon-2019/): Exciting news: We’re hosting the second annual QueryCon on June 20th-21st in New York City, co-sponsored by Kolide and Carbon Black!
- [User-Friendly Fuzzing with Sienna Locomotive](https://blog.trailofbits.com/2019/04/08/user-friendly-fuzzing-with-sienna-locomotive/): Fuzzing is a great way to find bugs in software, but many developers don’t use it. We hope to change that today with the release of Sienna Locomotive,...
- [Performing Concolic Execution on Cryptographic Primitives](https://blog.trailofbits.com/2019/04/01/performing-concolic-execution-on-cryptographic-primitives/): Alan Cao
- [Fuzzing In The Year 2000](https://blog.trailofbits.com/2019/03/28/fuzzing-in-the-year-2000/): It is time for the second installment of our efforts to reproduce original fuzzing research on modern systems. If you haven’t yet, please read the fir...
- [What Application Developers Need To Know About TLS Early Data (0RTT)](https://blog.trailofbits.com/2019/03/25/what-application-developers-need-to-know-about-tls-early-data-0rtt/): TLS 1.3 represents the culmination of over two decades of experience in deploying large-scale transport security. For the most part it simplifies and ...
- [Symbolic Path Merging in Manticore](https://blog.trailofbits.com/2019/01/25/symbolic-path-merging-in-manticore/): Each year, Trail of Bits runs a month-long winter internship “winternship” program. This year we were happy to host 4 winterns who contributed to 3 pr...
- [Fuzzing an API with DeepState (Part 2)](https://blog.trailofbits.com/2019/01/23/fuzzing-an-api-with-deepstate-part-2/): Alex Groce, Associate Professor, School of Informatics, Computing and Cyber Systems, Northern Arizona University
- [Fuzzing an API with DeepState (Part 1)](https://blog.trailofbits.com/2019/01/22/fuzzing-an-api-with-deepstate-part-1/): Alex Groce, Associate Professor, School of Informatics, Computing and Cyber Systems, Northern Arizona University
- [How McSema Handles C++ Exceptions](https://blog.trailofbits.com/2019/01/21/how-mcsema-handles-c-exceptions/): C++ programs using exceptions are problematic for binary lifters. The non-local control-flow “throw” and “catch” operations that appear in C++ source ...
- [Empire Hacking: Ethereum Edition 2](https://blog.trailofbits.com/2019/01/18/empire-hacking-ethereum-edition-2/): On December 12, over 150 attendees joined a special, half-day Empire Hacking to learn about pitfalls in smart contract security and how to avoid them....
- [How to write a rootkit without really trying](https://blog.trailofbits.com/2019/01/17/how-to-write-a-rootkit-without-really-trying/): We open-sourced a fault injection tool, KRF, that uses kernel-space syscall interception. You can use it today to find faulty assumptions (and resulta...
- [On Bounties and Boffins](https://blog.trailofbits.com/2019/01/14/on-bounties-and-boffins/): Trying to make a living as a programmer participating in bug bounties is the same as convincing yourself that you’re good enough at Texas Hold ‘Em to ...
- [What do La Croix, octonions, and Second Life have in common?](https://blog.trailofbits.com/2019/01/02/what-do-la-croix-octonions-and-second-life-have-in-common/): This year for CSAW CTF, Trail of Bits contributed two cryptography problems. In the first problem, you could combine two bugs to break DSA much like t...

## 2018 Blog Posts

- [Fuzzing Like It’s 1989](https://blog.trailofbits.com/2018/12/31/fuzzing-like-its-1989/): With 2019 a day away, let’s reflect on the past to see how we can improve. Yes, let’s take a long look back 30 years and reflect on the original fuzzi...
- [$10,000 research fellowships for underrepresented talent](https://blog.trailofbits.com/2018/12/20/10000-research-fellowships-for-underrepresented-talent/): The Trail of Bits SummerCon Fellowship program is now accepting applications from emerging security researchers with excellent project ideas. Fellows ...
- [CSAW CTF Crypto Challenge: Breaking DSA](https://blog.trailofbits.com/2018/12/17/csaw-ctf-crypto-challenge-breaking-dsa/): The Trail of Bits cryptographic services team contributed two cryptography CTF challenges to the recent CSAW CTF. Today we’re going to cover the easie...
- [10 Rules for the Secure Use of Cryptocurrency Hardware Wallets](https://blog.trailofbits.com/2018/11/27/10-rules-for-the-secure-use-of-cryptocurrency-hardware-wallets/): Earlier this year, the Web3 Foundation (W3F) commissioned Trail of Bits for a security review and assessment of the risks in storing cryptocurrency. E...
- [Return of the Blockchain Security Empire Hacking](https://blog.trailofbits.com/2018/11/19/return-of-the-blockchain-security-empire-hacking/): Remember last December’s Empire Hacking? The one where we dedicated the event to sharing the best information about blockchain and smart contract secu...
- [Trail of Bits @ Devcon IV Recap](https://blog.trailofbits.com/2018/11/16/trail-of-bits-devcon-iv-recap/): We wanted to make up for missing the first three Devcons, so we participated in this year’s event through a number of talks, a panel, and two training...
- [We crypto now](https://blog.trailofbits.com/2018/11/07/we-crypto-now/): Building and using cryptographic libraries is notoriously difficult. Even when each component of the system has been implemented correctly (quite diff...
- [How contract migration works](https://blog.trailofbits.com/2018/10/29/how-contract-migration-works/): Smart contracts can be compromised: they can have bugs, the owner’s wallet can be stolen, or they can be trapped due to an incorrect setting. If you d...
- [The Good, the Bad, and the Weird](https://blog.trailofbits.com/2018/10/26/the-good-the-bad-and-the-weird/): Let’s automatically identify weird machines in software.
- [A Guide to Post-Quantum Cryptography](https://blog.trailofbits.com/2018/10/22/a-guide-to-post-quantum-cryptography/): For many high-assurance applications such as TLS traffic, medical databases, and blockchains, forward secrecy is absolutely essential. It is not suffi...
- [Slither – a Solidity static analysis framework](https://blog.trailofbits.com/2018/10/19/slither-a-solidity-static-analysis-framework/): Slither is the first open-source static analysis framework for Solidity. Slither is fast and precise; it can find real vulnerabilities in a few second...
- [Introduction to Verifiable Delay Functions (VDFs)](https://blog.trailofbits.com/2018/10/12/introduction-to-verifiable-delay-functions-vdfs/): Finding randomness on the blockchain is hard. A classic mistake developers make when trying to acquire a random value on-chain is to use quantities li...
- [How to Spot Good Fuzzing Research](https://blog.trailofbits.com/2018/10/05/how-to-spot-good-fuzzing-research/): Of the nearly 200 papers on software fuzzing that have been published in the last three years, most of them—even some from high-impact conferences—are...
- [Ethereum security guidance for all](https://blog.trailofbits.com/2018/10/04/ethereum-security-guidance-for-all/): We came away from ETH Berlin with two overarching impressions: first, many developers were hungry for any guidance on security, and second; too few se...
- [Effortless security feature detection with Winchecksec](https://blog.trailofbits.com/2018/09/26/effortless-security-feature-detection-with-winchecksec/): We’re proud to announce the release of Winchecksec, a new open-source tool that detects security features in Windows binaries. Developed to satisfy ou...
- [Protecting Software Against Exploitation with DARPA’s CFAR](https://blog.trailofbits.com/2018/09/10/protecting-software-against-exploitation-with-darpas-cfar/): Today, we’re going to talk about a hard problem that we are working on as part of DARPA’s Cyber Fault-Tolerant Attack Recovery (CFAR) program: automat...
- [Rattle – an Ethereum EVM binary analysis framework](https://blog.trailofbits.com/2018/09/06/rattle-an-ethereum-evm-binary-analysis-framework/): Most smart contracts have no verified source code, but people still trust them to protect their cryptocurrency. What’s more, several large custodial s...
- [Contract upgrade anti-patterns](https://blog.trailofbits.com/2018/09/05/contract-upgrade-anti-patterns/): A popular trend in smart contract design is to promote the development of upgradable contracts. At Trail of Bits, we have reviewed many upgradable con...
- [Introducing windows-acl: working with ACLs in Rust](https://blog.trailofbits.com/2018/08/23/introducing-windows-acl-working-with-acls-in-rust/): Access Control Lists (ACLs) are an integral part of the Microsoft Windows security model. In addition to controlling access to secured resources, they...
- [Get an open-source security multiplier](https://blog.trailofbits.com/2018/08/22/get-an-open-source-security-multiplier/): An increasing number of organizations and companies (including the federal government) rely on open-source projects in their security operations archi...
- [Fault Analysis on RSA Signing](https://blog.trailofbits.com/2018/08/14/fault-analysis-on-rsa-signing/): Aditi Gupta
- [You could have invented that Bluetooth attack](https://blog.trailofbits.com/2018/08/01/bluetooth-invalid-curve-points/): A serious bluetooth bug has received quite a bit of attention lately. It’s a great find by Biham and Newman. Given BLE’s popularity in the patch-avers...
- [Optimizing Lifted Bitcode with Dead Store Elimination](https://blog.trailofbits.com/2018/07/06/optimizing-lifted-bitcode-with-dead-store-elimination/): Tim Alberdingk Thijm
- [Trail of Bits donates $100,000 to support young researchers through SummerCon](https://blog.trailofbits.com/2018/06/29/trail-of-bits-donates-100000-to-support-young-researchers-through-summercon/): We have a soft spot in our hearts for SummerCon. This event, the longest-running hacker conference in the US, is a great chance to host hacker friends...
- [Announcing the Trail of Bits osquery support group](https://blog.trailofbits.com/2018/06/27/announcing-the-trail-of-bits-osquery-support-group/): As great as it is, osquery could be a whole lot better. (Think write access for extensions, triggered responses upon detection, and even better perfor...
- [QueryCon 2018: our talks and takeaways](https://blog.trailofbits.com/2018/06/08/querycon-2018-our-talks-and-takeaways/): Sometimes a conference just gets it right. Good talks, single track, select engaged attendees, and no sales talks. It’s a recipe for success that Koli...
- [Manage your fleet’s firewalls with osquery](https://blog.trailofbits.com/2018/05/30/manage-your-fleets-firewalls-with-osquery/): We’re releasing an extension for osquery that lets you manage the local firewalls of your fleet.
- [Manage Santa within osquery](https://blog.trailofbits.com/2018/05/29/manage-santa-within-osquery/): We’re releasing an extension for osquery that lets you manage Google Santa without the need for a separate sync server.
- [Collect NTFS forensic information with osquery](https://blog.trailofbits.com/2018/05/28/collect-ntfs-forensic-information-with-osquery/): We’re releasing an extension for osquery that will let you dig deeper into the NTFS filesystem. It’s one more tool for incident response and data coll...
- [State Machine Testing with Echidna](https://blog.trailofbits.com/2018/05/03/state-machine-testing-with-echidna/): Property-based testing is a powerful technique for verifying arbitrary properties of a program via execution on a large set of inputs, typically gener...
- [What do you wish osquery could do?](https://blog.trailofbits.com/2018/04/10/what-do-you-wish-osquery-could-do/): Welcome to the third post in our series about osquery. So far, we’ve described how five enterprise security teams use osquery and reviewed the issues ...
- [How to prepare for a security review](https://blog.trailofbits.com/2018/04/06/how-to-prepare-for-a-security-audit/): You’ve just approved a security review of your codebase. Do you:
- [Vulnerability Modeling with Binary Ninja](https://blog.trailofbits.com/2018/04/04/vulnerability-modeling-with-binary-ninja/): This is Part 3 in a series of posts about the Binary Ninja Intermediate Language (BNIL) family. You can read Part 1 here and Part 2 here.
- [Use our suite of Ethereum security tools](https://blog.trailofbits.com/2018/03/23/use-our-suite-of-ethereum-security-tools/): Two years ago, when we began taking on blockchain security engagements, there were no tools engineered for the work. No static analyzers, fuzzers, or ...
- [An accessible overview of Meltdown and Spectre, Part 2](https://blog.trailofbits.com/2018/03/22/an-accessible-overview-of-meltdown-and-spectre-part-2/): This is the second half of our blog post on the Meltdown an Spectre vulnerabilities, describing Spectre Variant 1 (V1) and Spectre Variant 2 (V2). If ...
- [“AMD Flaws” Technical Summary](https://blog.trailofbits.com/2018/03/15/amd-flaws-technical-summary/): Two weeks ago, we were engaged by CTS Labs as independent consultants at our standard consulting rates to review and confirm the technical accuracy of...
- [Echidna, a smart fuzzer for Ethereum](https://blog.trailofbits.com/2018/03/09/echidna-a-smart-fuzzer-for-ethereum/): Today we released Echidna, our next-generation EVM smart fuzzer at EthCC. It’s the first-ever fuzzer to target smart contracts, and has powerful featu...
- [2017 in review](https://blog.trailofbits.com/2018/03/08/2017-in-review/): What a roller coaster of a year! Well, outside of our office. Inside, 2017 was excellent.
- [Parity Technologies engages Trail of Bits](https://blog.trailofbits.com/2018/02/09/parity-technologies-engages-trail-of-bits/): We’re helping Parity Technologies secure their Ethereum client. We’ll begin by auditing their codebase, and look forward to publishing results and the...
- [An accessible overview of Meltdown and Spectre, Part 1](https://blog.trailofbits.com/2018/01/30/an-accessible-overview-of-meltdown-and-spectre-part-1/): In the past few weeks the details of two critical design flaws in modern processors were finally revealed to the public. Much has been written about t...
- [Heavy lifting with McSema 2.0](https://blog.trailofbits.com/2018/01/23/heavy-lifting-with-mcsema-2-0/): Four years ago, we released McSema, our x86 to LLVM bitcode binary translator. Since then, it has stretched and flexed; we added x86-64 support, put i...

## 2017 Blog Posts

- [Videos from Ethereum-focused Empire Hacking](https://blog.trailofbits.com/2017/12/22/videos-from-ethereum-focused-empire-hacking/): On December 12, over 150 attendees learned how to write and hack secure smart contracts at the final Empire Hacking meetup of 2017. Thank you to every...
- [What are the current pain points of osquery?](https://blog.trailofbits.com/2017/12/21/osquery-pain-points/): You’re reading the second post in our four-part series about osquery. Read post number one for a snapshot of the tool’s current use, the reasons for i...
- [Announcing the Trail of Bits osquery extension repository](https://blog.trailofbits.com/2017/12/14/announcing-the-trail-of-bits-osquery-extension-repository/): Today, we are releasing access to our maintained repository of osquery extensions. Our first extension takes advantage of the Duo Labs EFIgy API to de...
- [Securing Ethereum at Empire Hacking](https://blog.trailofbits.com/2017/11/22/securing-ethereum-at-empire-hacking/): If you’re building real applications with blockchain technology and are worried about security, consider this meetup essential. Join us on December 12...
- [How are teams currently using osquery?](https://blog.trailofbits.com/2017/11/09/how-are-teams-currently-using-osquery/): In the year since we ported osquery to Windows, the operating system instrumentation and endpoint monitoring agent has attracted a great deal of atten...
- [Hands on the Ethernaut CTF](https://blog.trailofbits.com/2017/11/06/hands-on-the-ethernaut-ctf/): Last week Zeppelin released their Ethereum CTF, Ethernaut.
- [Trail of Bits joins the Enterprise Ethereum Alliance](https://blog.trailofbits.com/2017/10/19/trail-of-bits-joins-the-enterprise-ethereum-alliance/): We’re proud to announce that Trail of Bits has joined the Enterprise Ethereum Alliance (EEA), the world’s largest open source blockchain initiative. A...
- [Our team is growing](https://blog.trailofbits.com/2017/10/16/our-team-is-growing/): We’ve added five more to our ranks in the last two months, bringing our total size to 32 employees. Their resumes feature words and acronyms like ‘CTO...
- [iOS jailbreak detection toolkit now available](https://blog.trailofbits.com/2017/10/12/ios-jailbreak-detection-toolkit-now-available/): We now offer a library for developers to check if their apps are running on jailbroken phones. It includes the most comprehensive checks in the indust...
- [Tracking a stolen code-signing certificate with osquery](https://blog.trailofbits.com/2017/10/10/tracking-a-stolen-code-signing-certificate-with-osquery/): Recently, 2.27 million computers running Windows were infected with malware signed with a stolen certificate from the creators of a popular app called...
- [Microsoft didn’t sandbox Windows Defender, so I did](https://blog.trailofbits.com/2017/08/02/microsoft-didnt-sandbox-windows-defender-so-i-did/): Microsoft exposed their users to a lot of risks when they released Windows Defender without a sandbox. This surprised me. Sandboxing is one of the mos...
- [An extra bit of analysis for Clemency](https://blog.trailofbits.com/2017/07/30/an-extra-bit-of-analysis-for-clemency/): This year’s DEF CON CTF used a unique hardware architecture, cLEMENCy, and only released a specification and reference tooling for it 24 hours before ...
- [Magic with Manticore](https://blog.trailofbits.com/2017/05/15/magic-with-manticore/): Manticore is a next-generation binary analysis tool with a simple yet powerful API for symbolic execution, taint analysis, and instrumentation. Using ...
- [Manticore: Symbolic execution for humans](https://blog.trailofbits.com/2017/04/27/manticore-symbolic-execution-for-humans/): Earlier this week, we open-sourced a tool we rely on for dynamic binary analysis: Manticore! Manticore helps us quickly take advantage of symbolic exe...
- [A walk down memory lane](https://blog.trailofbits.com/2017/04/14/a-walk-down-memory-lane/): Admit it. Every now and then someone does something, and you think: “I also had that idea!” You feel validated — a kindred spirit has had the same int...
- [April means Infiltrate](https://blog.trailofbits.com/2017/03/23/april-means-infiltrate/): Break out your guayabera, it’s time for Infiltrate. Trail of Bits has attended every Infiltrate and has been a sponsor since 2015. The majority of the...
- [McSema: I’m liftin’ it](https://blog.trailofbits.com/2017/03/14/mcsema-im-liftin-it/): McSema, our x86 machine code to LLVM bitcode binary translator, just got a fresh coat of paint. Last week we held a successful hackathon that produced...
- [The Challenges of Deploying Security Mitigations](https://blog.trailofbits.com/2017/02/20/the-challenges-of-deploying-security-mitigations/): This blog has promoted control flow integrity (CFI) as a game changing security mitigation and encouraged its use. We wanted to take our own security ...
- [The Smart Fuzzer Revolution](https://blog.trailofbits.com/2017/02/16/the-smart-fuzzer-revolution/): I recently had the privilege of giving a keynote at BSidesLisbon. I had a great time at the conference, and I’d like to thank Bruno Morisson for invit...
- [Devirtualizing C++ with Binary Ninja](https://blog.trailofbits.com/2017/02/13/devirtualizing-c-with-binary-ninja/): In my first blog post, I introduced the general structure of Binary Ninja’s Low Level IL (LLIL), as well as how to traverse and manipulate it with the...
- [Breaking Down Binary Ninja’s Low Level IL](https://blog.trailofbits.com/2017/01/31/breaking-down-binary-ninjas-low-level-il/): Hi, I’m Josh. I recently joined the team at Trail of Bits, and I’ve been an evangelist and plugin writer for the Binary Ninja reversing platform for a...
- [2016 Year in Review](https://blog.trailofbits.com/2017/01/09/2016-year-in-review/): John Oliver may have written off 2016, but we’re darn proud of all that we accomplished and contributed this year.

## 2016 Blog Posts

- [Let’s talk about CFI: Microsoft Edition](https://blog.trailofbits.com/2016/12/27/lets-talk-about-cfi-microsoft-edition/): We’re back with our promised second installment discussing control flow integrity. This time, we will talk about Microsoft’s implementation of control...
- [Meet Algo, the VPN that works](https://blog.trailofbits.com/2016/12/12/meet-algo-the-vpn-that-works/): I think you’ll agree when I say: there’s no VPN option on the market designed with equal emphasis on security and ease of use.
- [Shin GRR: Make Fuzzing Fast Again](https://blog.trailofbits.com/2016/11/02/shin-grr-make-fuzzing-fast-again/): 
- [Come Find Us at O’Reilly Security](https://blog.trailofbits.com/2016/10/26/come-find-us-at-oreilly-security/): We’re putting our money where our mouth is again. In continued support for New York’s growing infosec community we’re excited to sponsor the upcoming ...
- [Let’s talk about CFI: clang edition](https://blog.trailofbits.com/2016/10/17/lets-talk-about-cfi-clang-edition/): Our previous blog posts often mentioned control flow integrity, or CFI, but we have never explained what CFI is, how to use it, or why you should care...
- [Automated Code Audit’s First Customer](https://blog.trailofbits.com/2016/10/04/first-ever-automated-code-audit/): Last month our Cyber Reasoning System (CRS) -developed for DARPA’s Cyber Grand Challenge– audited a much larger amount of code in less time, in greate...
- [Windows network security now easier with osquery](https://blog.trailofbits.com/2016/09/27/windows-network-security-now-easier-with-osquery/): Today, Facebook announced the successful completion of our work: osquery for Windows.
- [Plug into New York’s Infosec Community](https://blog.trailofbits.com/2016/09/12/plug-into-new-yorks-infosec-community/): Between the city’s size and the wide spectrum of the security industry, it’s easy to feel lost. Where are ‘your people?’ How can you find talks that i...
- [Work For Us: Fall and Winter Internship Opportunities](https://blog.trailofbits.com/2016/08/09/work-for-us-fall-and-winter-internship-opportunities/): If you’re studying in a degree program, and you thrive at the intersection of software development and cyber security, you should apply to our fall or...
- [A fuzzer and a symbolic executor walk into a cloud](https://blog.trailofbits.com/2016/08/02/engineering-solutions-to-hard-program-analysis-problems/): Finding bugs in programs is hard. Automating the process is even harder. We tackled the harder problem and produced two production-quality bug-finding...
- [Your tool works better than mine? Prove it.](https://blog.trailofbits.com/2016/08/01/your-tool-works-better-than-mine-prove-it/): No doubt, DARPA’s Cyber Grand Challenge (CGC) will go down in history for advancing the state of the art in a variety of fields: symbolic execution, b...
- [Why I didn’t catch any Pokemon today](https://blog.trailofbits.com/2016/07/11/why-i-didnt-catch-any-pokemon-today/): tl;dr While the internet went crazy today, we went fact finding. Here are our notes on Pokemon Go’s permissions to your Google account.
- [Start using the Secure Enclave Crypto API](https://blog.trailofbits.com/2016/06/28/start-using-the-secure-enclave-crypto-api/): tl;dr – Tidas is now open source. Let us know if your company wants help trying it out.
- [It’s time to take ownership of our image](https://blog.trailofbits.com/2016/06/23/its-time-to-take-ownership-of-our-image/): Gloves
Goggles
Checkered body suits
- [2000 cuts with Binary Ninja](https://blog.trailofbits.com/2016/06/03/2000-cuts-with-binary-ninja/): Using Vector35’s Binary Ninja, a promising new interactive static analysis and reverse engineering platform, I wrote a script that generated “exploits...
- [Empire Hacking Turns One](https://blog.trailofbits.com/2016/05/19/empire-hacking-turns-one/): In the year since we started this bi-monthly meetup, we’ve been thrilled by the community that it has attracted. We’ve had some excellent presentation...
- [ProtoFuzz: A Protobuf Fuzzer](https://blog.trailofbits.com/2016/05/18/protofuzz-a-protobuf-fuzzer/): Google’s Protocol Buffers (protobuf) is a common method of serializing data, typically found in distributed applications. Protobufs simplify the gener...
- [The DBIR’s ‘Forest’ of Exploit Signatures](https://blog.trailofbits.com/2016/05/05/the-dbirs-forest-of-exploit-signatures/): If you follow the recommendations in the 2016 Verizon Data Breach Investigations Report (DBIR), you will expose your organization to more risk, not le...
- [Hacker Handle Bounty](https://blog.trailofbits.com/2016/04/01/hacker-handle-bounty/): It’s time to close this chapter of our industry’s past. To distance ourselves from the World Wrestling Federation and comic book superheroes.
- [The Problem with Dynamic Program Analysis](https://blog.trailofbits.com/2016/03/09/the-problem-with-dynamic-program-analysis/): Developers have access to tools like AddressSanitizer and Valgrind that will tell them when the code that they’re running accesses uninitialized memor...
- [Apple can comply with the FBI court order](https://blog.trailofbits.com/2016/02/17/apple-can-comply-with-the-fbi-court-order/): Earlier today, a federal judge ordered Apple to comply with the FBI’s request for technical assistance in the recovery of the San Bernadino gunmen’s i...
- [Tidas: a new service for building password-less apps](https://blog.trailofbits.com/2016/02/09/tidas-a-new-service-for-building-password-less-apps/): For most mobile app developers, password management has as much appeal as a visit to the dentist. You do it because you have to, but it is annoying an...
- [Join us at Etsy’s Code as Craft](https://blog.trailofbits.com/2016/02/04/join-us-at-code-as-craft/): We’re excited to announce that Sophia D’Antoine will be the next featured speaker at Etsy’s Code as Craft series on Wednesday, February 10th from 6:30...
- [Software Security Ideas Ahead of Their Time](https://blog.trailofbits.com/2016/02/02/software-security-ideas-ahead-of-their-time/): Every good security researcher has a well-curated list of blogs they subscribe to. At Trail of Bits, given our interest in software security and its i...
- [Hacking for Charity: Automated Bug-finding in LibOTR](https://blog.trailofbits.com/2016/01/13/hacking-for-charity-automated-bug-finding-in-libotr/): At the end of last year, we had some free time to explore new and interesting uses of the automated bug-finding technology we developed for the DARPA ...
- [2015 In Review](https://blog.trailofbits.com/2016/01/07/2015-in-review/): Now that the new year is upon us, we can look back and take assessment of 2015. The past year saw Trail of Bits continuing our prior work, such as aut...
- [Let’s Encrypt the Internet](https://blog.trailofbits.com/2016/01/05/lets-encrypt-the-internet/): We’re excited to announce our financial support for Let’s Encrypt, the open, automated and free SSL Certificate Authority (CA) that went into public b...

## 2015 Blog Posts

- [Self-Hosted Video Chat with Tuber](https://blog.trailofbits.com/2015/12/15/self-hosted-video-chat-with-tuber/): Today, we’re releasing the source code to our self-hosted video chat platform, Tuber Time Communications (or just “Tuber”). We’ve been using Tuber for...
- [Why we give so much to CSAW](https://blog.trailofbits.com/2015/10/30/why-we-give-so-much-to-csaw/): In just a couple of weeks, tens of thousands of students and professionals from all over the world will tune in to cheer on their favorite teams in si...
- [Summer @ Trail of Bits](https://blog.trailofbits.com/2015/09/10/summer-trail-of-bits/): This summer I’ve had the incredible opportunity to work with Trail of Bits as a high school intern. In return, I am obligated to write a blog post abo...
- [Flare-On Reversing Challenges 2015](https://blog.trailofbits.com/2015/09/09/flare-on-reversing-challenges-2015/): This summer FireEye’s FLARE team hosted its second annual Flare-On Challenge targeting reverse engineers, malware analysts, and security professionals...
- [Hardware Side Channels in the Cloud](https://blog.trailofbits.com/2015/07/21/hardware-side-channels-in-the-cloud/): At REcon 2015, I demonstrated a new hardware side channel which targets co-located virtual machines in the cloud. This attack exploits the CPU’s pipel...
- [How We Fared in the Cyber Grand Challenge](https://blog.trailofbits.com/2015/07/15/how-we-fared-in-the-cyber-grand-challenge/): The Cyber Grand Challenge qualifying event was held on June 3rd, at exactly noon Eastern time. At that instant, our Cyber Reasoning System (CRS) was g...
- [How to Harden Your Google Apps](https://blog.trailofbits.com/2015/07/07/how-to-harden-your-google-apps/): Never let a good incident go to waste.
- [Introducing the RubySec Field Guide](https://blog.trailofbits.com/2015/06/08/introducing-the-rubysec-field-guide/): Vulnerabilities have been discovered in Ruby applications with the potential to affect vast swathes of the Internet and attract attackers to lucrative...
- [Closing the Windows Gap](https://blog.trailofbits.com/2015/05/13/closing-the-windows-gap/): The security research community is full of grey beards that earned their stripes writing exploits against mail servers, domain controllers, and TCP/IP...
- [Empire Hacking, a New Meetup in NYC](https://blog.trailofbits.com/2015/05/05/empire-hacking/): Today we are launching Empire Hacking, a bi-monthly meetup that focuses on pragmatic security research and new discoveries in attack and defense.
- [The Foundation of 2015: 2014 in Review](https://blog.trailofbits.com/2015/01/05/the-foundation-of-2015-2014-in-review/): We need to do more to protect ourselves. 2014 overflowed with front-page proof: Apple, Target, JPMorgan Chase, etc, etc.

## 2014 Blog Posts

- [Close Encounters with Symbolic Execution (Part 2)](https://blog.trailofbits.com/2014/12/04/close-encounters-with-symbolic-execution-part-2/): This is part two of a two-part blog post that shows how to use KLEE with mcsema to symbolically execute Linux binaries (see the first post!). This par...
- [Close Encounters with Symbolic Execution](https://blog.trailofbits.com/2014/11/25/close-encounters-with-symbolic-execution/): At THREADS 2014, I demonstrated a new capability of mcsema that enables the use of KLEE, a symbolic execution framework, on software available only in...
- [Speaker Lineup for THREADS ’14: Scaling Security](https://blog.trailofbits.com/2014/10/02/threads-14-scaling-security/): For every security engineer you train, there are 20 or more developers writing code with potential vulnerabilities. There’s no human way to keep up. W...
- [We’re Sponsoring the NYU-Poly Women’s Cybersecurity Symposium](https://blog.trailofbits.com/2014/09/29/nyu-womens-cybersecurity-symposium/): 
- [Enabling Two-Factor Authentication (2FA) for Apple ID and DropBox](https://blog.trailofbits.com/2014/09/02/enabling-two-factor-authentication-2fa-for-apple-id-and-dropbox/): In light of the recent compromises, you’re probably wondering what could have been done to prevent such attacks. According to some unverified articles...
- [ReMASTering Applications by Obfuscating during Compilation](https://blog.trailofbits.com/2014/08/20/remastering-applications-by-obfuscating-during-compilation/): In this post, we discuss the creation of a novel software obfuscation toolkit, MAST, implemented in the LLVM compiler and suitable for denying program...
- [McSema is Officially Open Source!](https://blog.trailofbits.com/2014/08/07/mcsema-is-officially-open-source/): We are proud to announce that McSema is now open source! McSema is a framework for analyzing and transforming machine-code programs to LLVM bitcode. I...
- [Education Initiative Spotlight: THREADS Call for Papers](https://blog.trailofbits.com/2014/08/01/education-initiative-spotlight-threads-call-for-papers/): We would like to share the call for papers for THREADS 2014, a research and development conference that is part of NYU-Poly’s Cyber Security Awareness...
- [Education Initiative Spotlight: Build it Break it](https://blog.trailofbits.com/2014/07/30/education-initiative-spotlight-build-it-break-it/): Build it Break it is a first-of-its-kind security challenge run by UMD
- [Education Initiative Spotlight: CSAW Summer Program for Women](https://blog.trailofbits.com/2014/07/28/education-initiative-spotlight-csaw-summer-program-for-women/): At Trail of Bits we are proud of our roots in academia and research, and we believe it is important to promote cyber security education for students o...
- [Trail of Bits Adds Mobile Security Researcher Nicholas DePetrillo to Growing Team](https://blog.trailofbits.com/2014/07/15/trail-of-bits-adds-mobile-security-researcher-nicholas-depetrillo-to-growing-team/): New York, NY (July 15th, 2014)—Veteran computer security researcher Nicholas DePetrillo has joined Trail of Bits, the New York-based security company,...
- [A Preview of McSema](https://blog.trailofbits.com/2014/06/23/a-preview-of-mcsema/): On June 28th Artem Dinaburg and Andrew Ruef will be speaking at REcon 2014 about a project named McSema. McSema is a framework for translating x86 bin...
- [We’ve Moved!](https://blog.trailofbits.com/2014/06/04/weve-moved/): Trail of Bits headquarters has moved! Located in the heart of the financial district, our new office features a unique design, cool modern decor, and ...
- [Dear DARPA: Challenge Accepted.](https://blog.trailofbits.com/2014/06/03/dear-darpa-challenge-accepted/): 
- [Trail of Bits Releases Capture the Flag Field Guide](https://blog.trailofbits.com/2014/05/20/trail-of-bits-releases-the-capture-the-flag-field-guide/): New York, NY (May 20, 2014)–Security researchers at Trail of Bits today introduced the CTF Field Guide (Capture the Flag), a freely available, self-gu...
- [Using Static Analysis and Clang To Find Heartbleed](https://blog.trailofbits.com/2014/04/27/using-static-analysis-and-clang-to-find-heartbleed/): Friday night I sat down with a glass of Macallan 15 and decided to write a static checker that would find the Heartbleed bug. I decided that I would w...
- [Introducing Javelin](https://blog.trailofbits.com/2014/02/24/introducing-javelin/): 
- [Semantic Analysis of Native Programs with CodeReason](https://blog.trailofbits.com/2014/02/23/semantic-analysis-of-native-programs-introducing-codereason/): Have you ever wanted to make a query into a native mode program asking about program locations that write a specific value to a register? Have you eve...

## 2013 Blog Posts

- [iVerify is now available on Github](https://blog.trailofbits.com/2013/07/24/iverify-is-now-available-on-github/): Today we’re excited to release an open-source version of iVerify!
- [Free Ruby Security Workshop](https://blog.trailofbits.com/2013/06/03/free-ruby-security-workshop/): We interrupt our regularly scheduled programming to bring you an important announcement: On Thursday, June 6th, just in time for SummerCon, we will be...
- [Writing Exploits with the Elderwood Kit (Part 2)](https://blog.trailofbits.com/2013/05/20/writing-exploits-with-the-elderwood-kit-part-2/): In the final part of our three-part series, we investigate the how the toolkit user gained control of program flow and what their strategy means for t...
- [Writing Exploits with the Elderwood Kit (Part 1)](https://blog.trailofbits.com/2013/05/14/writing-exploits-with-the-elderwood-kit-part-1/): In the second part of our three-part series, we investigate the tools provided by the Elderwood kit for developing exploits from discovered vulnerabil...
- [Elderwood and the Department of Labor Hack](https://blog.trailofbits.com/2013/05/13/elderwood-and-the-department-of-labor-hack/): Recently, the Department of Labor (DoL) and several other websites were compromised to host a new zero-day exploit in Internet Explorer 8 (CVE-2013-13...

## 2012 Blog Posts

- [Ending the Love Affair with ExploitShield](https://blog.trailofbits.com/2012/10/29/ending-the-love-affair-with-exploitshield/): ExploitShield has been marketed as offering protection “against all known and unknown 0-day day vulnerability exploits, protecting users where traditi...
- [Analyzing the MD5 collision in Flame](https://blog.trailofbits.com/2012/06/11/analyzing-the-md5-collision-in-flame/): One of the more interesting aspects of the Flame malware was the MD5 collision attack that was used to infect new machines through Windows Update. MD5...

