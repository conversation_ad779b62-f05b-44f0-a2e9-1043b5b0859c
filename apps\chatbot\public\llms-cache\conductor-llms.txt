# Conductor Docs

## Docs

- [What is Conductor?](https://docs.conductor.is/about.md): The best QuickBooks Desktop integration on the planet.
- [Create an auth session](https://docs.conductor.is/api-ref/auth-sessions/create.md): To launch the authentication flow, create an auth session and pass the returned session's `authFlowUrl` to the client for your end-user to visit in their browser. Demo: https://connect.conductor.is/qbd/demo
- [The Auth Session object](https://docs.conductor.is/api-ref/auth-sessions/object.md): An auth session is the secure way to programmatically launch the client-side Conductor authentication flow that lets your users connect their accounts to your integration.
- [Authentication](https://docs.conductor.is/api-ref/authentication.md)
- [Create an end-user](https://docs.conductor.is/api-ref/end-users/create.md): Creates an end-user.
- [Delete an end-user](https://docs.conductor.is/api-ref/end-users/delete.md): Permanently deletes an end-user object and all of its connections.
- [List all end-users](https://docs.conductor.is/api-ref/end-users/list.md): Returns a list of your end-users.
- [The End-User object](https://docs.conductor.is/api-ref/end-users/object.md): An end-user represents a user of your application. Use it to connect integrations.
- [Retrieve an end-user](https://docs.conductor.is/api-ref/end-users/retrieve.md): Retrieves an end-user object.
- [The Error object](https://docs.conductor.is/api-ref/errors.md): The standard error format that the Conductor API returns for all errors.
- [Missing QBD types](https://docs.conductor.is/api-ref/missing-qbd-types.md)
- [Pagination](https://docs.conductor.is/api-ref/pagination.md): Learn how to efficiently retrieve large datasets in API v2 from QuickBooks Desktop using cursor-based pagination.
- [Create an account](https://docs.conductor.is/api-ref/qbd/accounts/create.md): Creates a new financial account.
- [List all accounts](https://docs.conductor.is/api-ref/qbd/accounts/list.md): Returns a list of accounts. NOTE: QuickBooks Desktop does not support pagination for accounts; hence, there is no `cursor` parameter. Users typically have few accounts.
- [The Account object](https://docs.conductor.is/api-ref/qbd/accounts/object.md): An account in QuickBooks Desktop represents a financial account used to track money and transactions. It can be customized with features like hierarchical sub-accounts, account numbers, and opening balances. Accounts form the foundation of the chart of accounts and can represent various types like bank accounts, credit cards, income, expense, and other financial categories.
- [Retrieve an account](https://docs.conductor.is/api-ref/qbd/accounts/retrieve.md): Retrieves an account by ID.
- [Update an account](https://docs.conductor.is/api-ref/qbd/accounts/update.md): Updates an existing financial account.
- [Create a bill check payment](https://docs.conductor.is/api-ref/qbd/bill-check-payments/create.md): Creates a new bill check payment.
- [Delete a bill check payment](https://docs.conductor.is/api-ref/qbd/bill-check-payments/delete.md): Permanently deletes a a bill check payment. The deletion will fail if the bill check payment is currently in use or has any linked transactions that are in use.
- [List all bill check payments](https://docs.conductor.is/api-ref/qbd/bill-check-payments/list.md): Returns a list of bill check payments. Use the `cursor` parameter to paginate through the results.
- [The Bill Check Payment object](https://docs.conductor.is/api-ref/qbd/bill-check-payments/object.md): A bill check payment records a payment made by check to pay off one or more vendor bills. It reduces accounts payable and decreases the bank account balance. This transaction links the original bill(s) with the payment, allowing QuickBooks to track which bills have been paid and maintain accurate vendor balances.
- [Retrieve a bill check payment](https://docs.conductor.is/api-ref/qbd/bill-check-payments/retrieve.md): Retrieves a bill check payment by ID.
- [Update a bill check payment](https://docs.conductor.is/api-ref/qbd/bill-check-payments/update.md): Updates an existing bill check payment.
- [Create a bill credit card payment](https://docs.conductor.is/api-ref/qbd/bill-credit-card-payments/create.md): Creates a new bill credit card payment.
- [Delete a bill credit card payment](https://docs.conductor.is/api-ref/qbd/bill-credit-card-payments/delete.md): Permanently deletes a a bill credit card payment. The deletion will fail if the bill credit card payment is currently in use or has any linked transactions that are in use.
- [List all bill credit card payments](https://docs.conductor.is/api-ref/qbd/bill-credit-card-payments/list.md): Returns a list of bill credit card payments. Use the `cursor` parameter to paginate through the results.
- [The Bill Credit Card Payment object](https://docs.conductor.is/api-ref/qbd/bill-credit-card-payments/object.md): A bill credit card payment records a payment made via credit card to pay off one or more vendor bills. It reduces accounts payable and increases the credit card account balance. This transaction links the original bill(s) with the payment, allowing QuickBooks to track which bills have been paid and maintain accurate vendor balances.
- [Retrieve a bill credit card payment](https://docs.conductor.is/api-ref/qbd/bill-credit-card-payments/retrieve.md): Retrieves a bill credit card payment by ID.
- [Create a bill](https://docs.conductor.is/api-ref/qbd/bills/create.md): Creates a new bill.
- [Delete a bill](https://docs.conductor.is/api-ref/qbd/bills/delete.md): Permanently deletes a a bill. The deletion will fail if the bill is currently in use or has any linked transactions that are in use.
- [List all bills](https://docs.conductor.is/api-ref/qbd/bills/list.md): Returns a list of bills. Use the `cursor` parameter to paginate through the results.
- [The Bill object](https://docs.conductor.is/api-ref/qbd/bills/object.md): A bill represents an obligation to pay a vendor for goods or services received. It records the amount owed, due date, and payment terms, and increases accounts payable. Bills can be partially paid over time and may be linked to purchase orders or item receipts.
- [Retrieve a bill](https://docs.conductor.is/api-ref/qbd/bills/retrieve.md): Retrieves a bill by ID.
- [Update a bill](https://docs.conductor.is/api-ref/qbd/bills/update.md): Updates an existing bill.
- [Create a check](https://docs.conductor.is/api-ref/qbd/checks/create.md): Creates a new check.
- [Delete a check](https://docs.conductor.is/api-ref/qbd/checks/delete.md): Permanently deletes a a check. The deletion will fail if the check is currently in use or has any linked transactions that are in use.
- [List all checks](https://docs.conductor.is/api-ref/qbd/checks/list.md): Returns a list of checks. Use the `cursor` parameter to paginate through the results.
- [The Check object](https://docs.conductor.is/api-ref/qbd/checks/object.md): A check represents a payment made from a bank account, typically via paper check. It records the withdrawal of funds paid to a vendor, employee, or other payee. The transaction reduces the balance of the specified bank account and can be linked to bills or other transactions being paid.
- [Retrieve a check](https://docs.conductor.is/api-ref/qbd/checks/retrieve.md): Retrieves a check by ID.
- [Update a check](https://docs.conductor.is/api-ref/qbd/checks/update.md): Updates an existing check.
- [Create a class](https://docs.conductor.is/api-ref/qbd/classes/create.md): Creates a new class.
- [List all classes](https://docs.conductor.is/api-ref/qbd/classes/list.md): Returns a list of classes. NOTE: QuickBooks Desktop does not support pagination for classes; hence, there is no `cursor` parameter. Users typically have few classes.
- [The Class object](https://docs.conductor.is/api-ref/qbd/classes/object.md): A class is a category used to group QuickBooks objects into meaningful categories. For example, classes can be used to classify transactions by department, location, or type of work.
- [Retrieve a class](https://docs.conductor.is/api-ref/qbd/classes/retrieve.md): Retrieves a class by ID.
- [Update a class](https://docs.conductor.is/api-ref/qbd/classes/update.md): Updates an existing class.
- [The Company object](https://docs.conductor.is/api-ref/qbd/company/object.md): Detailed information about the connected QuickBooks company file, including company address, legal name, preferences, and subscribed services.
- [Retrieve company file info](https://docs.conductor.is/api-ref/qbd/company/retrieve.md): Returns detailed information about the connected QuickBooks company file, including company address, legal name, preferences, and subscribed services. Note that company information cannot be modified through the API, only through the QuickBooks Desktop user interface.
- [Create a credit card charge](https://docs.conductor.is/api-ref/qbd/credit-card-charges/create.md): Creates a new credit card charge for the specified account.
- [Delete a credit card charge](https://docs.conductor.is/api-ref/qbd/credit-card-charges/delete.md): Permanently deletes a a credit card charge. The deletion will fail if the credit card charge is currently in use or has any linked transactions that are in use.
- [List all credit card charges](https://docs.conductor.is/api-ref/qbd/credit-card-charges/list.md): Returns a list of credit card charges. Use the `cursor` parameter to paginate through the results.
- [The Credit Card Charge object](https://docs.conductor.is/api-ref/qbd/credit-card-charges/object.md): A credit card charge is a general charge incurred when a QuickBooks user makes a purchase using a credit card. Credit card charges for purchases can be tracked as expenses (in expense accounts) or as items.
- [Retrieve a credit card charge](https://docs.conductor.is/api-ref/qbd/credit-card-charges/retrieve.md): Retrieves a credit card charge by ID.
- [Update a credit card charge](https://docs.conductor.is/api-ref/qbd/credit-card-charges/update.md): Updates an existing credit card charge.
- [Create a credit card credit](https://docs.conductor.is/api-ref/qbd/credit-card-credits/create.md): Creates a new credit card credit for the specified account.
- [Delete a credit card credit](https://docs.conductor.is/api-ref/qbd/credit-card-credits/delete.md): Permanently deletes a a credit card credit. The deletion will fail if the credit card credit is currently in use or has any linked transactions that are in use.
- [List all credit card credits](https://docs.conductor.is/api-ref/qbd/credit-card-credits/list.md): Returns a list of credit card credits. Use the `cursor` parameter to paginate through the results.
- [The Credit Card Credit object](https://docs.conductor.is/api-ref/qbd/credit-card-credits/object.md): A credit card credit represents a credit or refund received from a vendor for returned merchandise, billing adjustment, or other credit. It reduces the balance owed on the credit card account.
- [Retrieve a credit card credit](https://docs.conductor.is/api-ref/qbd/credit-card-credits/retrieve.md): Retrieves a credit card credit by ID.
- [Update a credit card credit](https://docs.conductor.is/api-ref/qbd/credit-card-credits/update.md): Updates an existing credit card credit.
- [Create a credit memo](https://docs.conductor.is/api-ref/qbd/credit-memos/create.md): Creates a new credit memo.
- [Delete a credit memo](https://docs.conductor.is/api-ref/qbd/credit-memos/delete.md): Permanently deletes a a credit memo. The deletion will fail if the credit memo is currently in use or has any linked transactions that are in use.
- [List all credit memos](https://docs.conductor.is/api-ref/qbd/credit-memos/list.md): Returns a list of credit memos. Use the `cursor` parameter to paginate through the results.
- [The Credit Memo object](https://docs.conductor.is/api-ref/qbd/credit-memos/object.md): A credit memo records an amount owed to a customer (such as for returns, over-payments, or pre-payments), reducing their outstanding balance. The credit remains available (tracked in the `creditRemaining` field) until it's applied to other transactions (such as invoices or sales receipts) through a receive-payment's `applyToTransactions.applyCredits` field.
- [Retrieve a credit memo](https://docs.conductor.is/api-ref/qbd/credit-memos/retrieve.md): Retrieves a credit memo by ID.
- [Update a credit memo](https://docs.conductor.is/api-ref/qbd/credit-memos/update.md): Updates an existing credit memo.
- [Create a customer](https://docs.conductor.is/api-ref/qbd/customers/create.md): Creates a new customer.
- [List all customers](https://docs.conductor.is/api-ref/qbd/customers/list.md): Returns a list of customers. Use the `cursor` parameter to paginate through the results.
- [The Customer object](https://docs.conductor.is/api-ref/qbd/customers/object.md): A customer record in QuickBooks Desktop represents either a business or individual who purchases goods or services, or a specific job/project being performed for that customer. Jobs are treated as sub-customers and inherit billing information from their parent customer while allowing for job-specific details to be tracked.
- [Retrieve a customer](https://docs.conductor.is/api-ref/qbd/customers/retrieve.md): Retrieves a customer by ID.
- [Update a customer](https://docs.conductor.is/api-ref/qbd/customers/update.md): Updates an existing customer.
- [Create a date-driven term](https://docs.conductor.is/api-ref/qbd/date-driven-terms/create.md): Creates a new date-driven term.
- [List all date-driven terms](https://docs.conductor.is/api-ref/qbd/date-driven-terms/list.md): Returns a list of date-driven terms. NOTE: QuickBooks Desktop does not support pagination for date-driven terms; hence, there is no `cursor` parameter. Users typically have few date-driven terms.
- [The Date-Driven Term object](https://docs.conductor.is/api-ref/qbd/date-driven-terms/object.md): A date-driven term shows the day of the month by which payment is due and can include a discount for early payment.
- [Retrieve a date-driven term](https://docs.conductor.is/api-ref/qbd/date-driven-terms/retrieve.md): Retrieves a date-driven term by ID.
- [Create a discount item](https://docs.conductor.is/api-ref/qbd/discount-items/create.md): Creates a new discount item.
- [List all discount items](https://docs.conductor.is/api-ref/qbd/discount-items/list.md): Returns a list of discount items. Use the `cursor` parameter to paginate through the results.
- [The Discount Item object](https://docs.conductor.is/api-ref/qbd/discount-items/object.md): A discount item applies a percentage or fixed amount reduction to the total or subtotal of the line directly above it. Items must be subtotaled first because discounts only affect the preceding line. Unlike discounts for early payments, which use standard-terms or date-driven-terms. Important: Never specify a quantity in a transaction when using a discount item.
- [Retrieve a discount item](https://docs.conductor.is/api-ref/qbd/discount-items/retrieve.md): Retrieves a discount item by ID.
- [Update a discount item](https://docs.conductor.is/api-ref/qbd/discount-items/update.md): Updates an existing discount item.
- [Create an employee](https://docs.conductor.is/api-ref/qbd/employees/create.md): Creates a new employee.
- [List all employees](https://docs.conductor.is/api-ref/qbd/employees/list.md): Returns a list of employees. Use the `cursor` parameter to paginate through the results.
- [The Employee object](https://docs.conductor.is/api-ref/qbd/employees/object.md): An employee represents a person employed by the company. It stores personal information, employment details, and payroll data used for personnel management and payroll processing.
- [Retrieve an employee](https://docs.conductor.is/api-ref/qbd/employees/retrieve.md): Retrieves an employee by ID.
- [Update an employee](https://docs.conductor.is/api-ref/qbd/employees/update.md): Updates an existing employee.
- [Create an estimate](https://docs.conductor.is/api-ref/qbd/estimates/create.md): Creates a new estimate.
- [Delete an estimate](https://docs.conductor.is/api-ref/qbd/estimates/delete.md): Permanently deletes a an estimate. The deletion will fail if the estimate is currently in use or has any linked transactions that are in use.
- [List all estimates](https://docs.conductor.is/api-ref/qbd/estimates/list.md): Returns a list of estimates. Use the `cursor` parameter to paginate through the results.
- [The Estimate object](https://docs.conductor.is/api-ref/qbd/estimates/object.md): An estimate is a formal proposal detailing costs and terms for goods or services to a customer. It can be called a "bid" or "proposal" and uses similar fields to invoices in QuickBooks. As a non-posting transaction, it serves as a planning tool that can be converted to an invoice upon customer acceptance.
- [Retrieve an estimate](https://docs.conductor.is/api-ref/qbd/estimates/retrieve.md): Retrieves an estimate by ID.
- [Update an estimate](https://docs.conductor.is/api-ref/qbd/estimates/update.md): Updates an existing estimate.
- [Create an inventory adjustment](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/create.md): Creates a new inventory adjustment.
- [Delete an inventory adjustment](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/delete.md): Permanently deletes a an inventory adjustment. The deletion will fail if the inventory adjustment is currently in use or has any linked transactions that are in use.
- [List all inventory adjustments](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/list.md): Returns a list of inventory adjustments. NOTE: QuickBooks Desktop does not support pagination for inventory adjustments; hence, there is no `cursor` parameter. Users typically have few inventory adjustments.
- [The Inventory Adjustment object](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/object.md): An inventory adjustment records changes to inventory item quantities and values in QuickBooks Desktop, typically used to correct discrepancies between physical counts and system records, or to account for damage, theft, or other inventory changes that aren't related to purchases or sales.
- [Retrieve an inventory adjustment](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/retrieve.md): Retrieves an inventory adjustment by ID.
- [Update an inventory adjustment](https://docs.conductor.is/api-ref/qbd/inventory-adjustments/update.md): Updates an existing inventory adjustment.
- [Create an inventory assembly item](https://docs.conductor.is/api-ref/qbd/inventory-assembly-items/create.md): Creates a new inventory assembly item.
- [List all inventory assembly items](https://docs.conductor.is/api-ref/qbd/inventory-assembly-items/list.md): Returns a list of inventory assembly items. Use the `cursor` parameter to paginate through the results.
- [The Inventory Assembly Item object](https://docs.conductor.is/api-ref/qbd/inventory-assembly-items/object.md): An inventory assembly item is an item that is assembled or manufactured from inventory items. The items and/or assemblies that make up the assembly are called components.
- [Retrieve an inventory assembly item](https://docs.conductor.is/api-ref/qbd/inventory-assembly-items/retrieve.md): Retrieves an inventory assembly item by ID.
- [Update an inventory assembly item](https://docs.conductor.is/api-ref/qbd/inventory-assembly-items/update.md): Updates an existing inventory assembly item.
- [Create an inventory item](https://docs.conductor.is/api-ref/qbd/inventory-items/create.md): Creates a new inventory item.
- [List all inventory items](https://docs.conductor.is/api-ref/qbd/inventory-items/list.md): Returns a list of inventory items. Use the `cursor` parameter to paginate through the results.
- [The Inventory Item object](https://docs.conductor.is/api-ref/qbd/inventory-items/object.md): An inventory item is any merchandise or part that a business purchases, tracks as inventory, and then resells.
- [Retrieve an inventory item](https://docs.conductor.is/api-ref/qbd/inventory-items/retrieve.md): Retrieves an inventory item by ID.
- [Update an inventory item](https://docs.conductor.is/api-ref/qbd/inventory-items/update.md): Updates an existing inventory item.
- [Create an inventory site](https://docs.conductor.is/api-ref/qbd/inventory-sites/create.md): Creates a new inventory site.
- [List all inventory sites](https://docs.conductor.is/api-ref/qbd/inventory-sites/list.md): Returns a list of inventory sites. NOTE: QuickBooks Desktop does not support pagination for inventory sites; hence, there is no `cursor` parameter. Users typically have few inventory sites.
- [The Inventory Site object](https://docs.conductor.is/api-ref/qbd/inventory-sites/object.md): An inventory site is a location where inventory is stored. For example, a company might have a warehouse, a stockroom, and a showroom, each of which is an inventory site. NOTE: Inventory sites require QuickBooks Enterprise with an Advanced Inventory subscription.
- [Retrieve an inventory site](https://docs.conductor.is/api-ref/qbd/inventory-sites/retrieve.md): Retrieves an inventory site by ID.
- [Update an inventory site](https://docs.conductor.is/api-ref/qbd/inventory-sites/update.md): Updates an existing inventory site.
- [Create an invoice](https://docs.conductor.is/api-ref/qbd/invoices/create.md): Creates a new invoice.
- [Delete an invoice](https://docs.conductor.is/api-ref/qbd/invoices/delete.md): Permanently deletes a an invoice. The deletion will fail if the invoice is currently in use or has any linked transactions that are in use.
- [List all invoices](https://docs.conductor.is/api-ref/qbd/invoices/list.md): Returns a list of invoices. Use the `cursor` parameter to paginate through the results.
- [The Invoice object](https://docs.conductor.is/api-ref/qbd/invoices/object.md): An invoice is a commercial document issued to customers that itemizes and records a transaction between buyer and seller. It lists the products or services provided, their quantities, prices, payment terms, and the total amount due. In QuickBooks, invoices are used to track accounts receivable and record sales transactions where payment was not made in full at the time of purchase.
- [Retrieve an invoice](https://docs.conductor.is/api-ref/qbd/invoices/retrieve.md): Retrieves an invoice by ID.
- [Update an invoice](https://docs.conductor.is/api-ref/qbd/invoices/update.md): Updates an existing invoice.
- [Create a journal entry](https://docs.conductor.is/api-ref/qbd/journal-entries/create.md): Creates a new journal entry.
- [Delete a journal entry](https://docs.conductor.is/api-ref/qbd/journal-entries/delete.md): Permanently deletes a a journal entry. The deletion will fail if the journal entry is currently in use or has any linked transactions that are in use.
- [List all journal entries](https://docs.conductor.is/api-ref/qbd/journal-entries/list.md): Returns a list of journal entries. Use the `cursor` parameter to paginate through the results.
- [The Journal Entry object](https://docs.conductor.is/api-ref/qbd/journal-entries/object.md): A journal entry is a direct way to record financial transactions by their debit and credit impacts on accounts, typically used for recording depreciation, adjusting entries, or other transactions that can't be entered through standard forms like bills or invoices.
- [Retrieve a journal entry](https://docs.conductor.is/api-ref/qbd/journal-entries/retrieve.md): Retrieves a journal entry by ID.
- [Update a journal entry](https://docs.conductor.is/api-ref/qbd/journal-entries/update.md): Updates an existing journal entry.
- [Create a non-inventory item](https://docs.conductor.is/api-ref/qbd/non-inventory-items/create.md): Creates a new non-inventory item.
- [List all non-inventory items](https://docs.conductor.is/api-ref/qbd/non-inventory-items/list.md): Returns a list of non-inventory items. Use the `cursor` parameter to paginate through the results.
- [The Non-Inventory Item object](https://docs.conductor.is/api-ref/qbd/non-inventory-items/object.md): A non-inventory item is any material or part that a business buys but does not keep on hand as inventory.
There are two types of non-inventory items: 1. Materials or parts that are part of the business's overhead (for example, office supplies) 2. Materials or parts that the business buys to finish a specific job and then charges back to the customer.
- [Retrieve a non-inventory item](https://docs.conductor.is/api-ref/qbd/non-inventory-items/retrieve.md): Retrieves a non-inventory item by ID.
- [Update a non-inventory item](https://docs.conductor.is/api-ref/qbd/non-inventory-items/update.md): Updates an existing non-inventory item.
- [Create a payroll wage item](https://docs.conductor.is/api-ref/qbd/payroll-wage-items/create.md): Creates a new payroll wage item.
- [List all payroll wage items](https://docs.conductor.is/api-ref/qbd/payroll-wage-items/list.md): Returns a list of payroll wage items. Use the `cursor` parameter to paginate through the results.
- [The Payroll Wage Item object](https://docs.conductor.is/api-ref/qbd/payroll-wage-items/object.md): A payroll wage item defines a type of payment scheme in QuickBooks Desktop, such as Regular Pay or Overtime Pay, that specifies how employee wages are calculated and tracked.
- [Retrieve a payroll wage item](https://docs.conductor.is/api-ref/qbd/payroll-wage-items/retrieve.md): Retrieves a payroll wage item by ID.
- [The Preferences object](https://docs.conductor.is/api-ref/qbd/preferences/object.md): The preferences that the QuickBooks administrator has set for all users of the connected company file.
- [Retrieve company file preferences](https://docs.conductor.is/api-ref/qbd/preferences/retrieve.md): Returns the preferences that the QuickBooks administrator has set for all users of the connected company file. Note that preferences cannot be modified through the API, only through the QuickBooks Desktop user interface.
- [Create a price level](https://docs.conductor.is/api-ref/qbd/price-levels/create.md): Creates a new price level.
- [List all price levels](https://docs.conductor.is/api-ref/qbd/price-levels/list.md): Returns a list of price levels. NOTE: QuickBooks Desktop does not support pagination for price levels; hence, there is no `cursor` parameter. Users typically have few price levels.
- [The Price Level object](https://docs.conductor.is/api-ref/qbd/price-levels/object.md): A price level is a configuration that establishes a default price for items. It can be applied to customers to automatically adjust item prices for those customers. Price levels can be either fixed percentages or per-item price levels.
- [Retrieve a price level](https://docs.conductor.is/api-ref/qbd/price-levels/retrieve.md): Retrieves a price level by ID.
- [Update a price level](https://docs.conductor.is/api-ref/qbd/price-levels/update.md): Updates an existing price level.
- [Create a purchase order](https://docs.conductor.is/api-ref/qbd/purchase-orders/create.md): Creates a new purchase order.
- [Delete a purchase order](https://docs.conductor.is/api-ref/qbd/purchase-orders/delete.md): Permanently deletes a a purchase order. The deletion will fail if the purchase order is currently in use or has any linked transactions that are in use.
- [List all purchase orders](https://docs.conductor.is/api-ref/qbd/purchase-orders/list.md): Returns a list of purchase orders. Use the `cursor` parameter to paginate through the results.
- [The Purchase Order object](https://docs.conductor.is/api-ref/qbd/purchase-orders/object.md): A purchase order represents a formal request for goods or services sent to a vendor. Since it is a non-posting transaction, it serves as a commitment to purchase but does not impact the company's financial statements.
- [Retrieve a purchase order](https://docs.conductor.is/api-ref/qbd/purchase-orders/retrieve.md): Retrieves a purchase order by ID.
- [Update a purchase order](https://docs.conductor.is/api-ref/qbd/purchase-orders/update.md): Updates an existing purchase order.
- [Create a receive-payment](https://docs.conductor.is/api-ref/qbd/receive-payments/create.md): Creates a new receive-payment.
- [Delete a receive-payment](https://docs.conductor.is/api-ref/qbd/receive-payments/delete.md): Permanently deletes a a receive-payment. The deletion will fail if the receive-payment is currently in use or has any linked transactions that are in use.
- [List all receive-payments](https://docs.conductor.is/api-ref/qbd/receive-payments/list.md): Returns a list of receive-payments. Use the `cursor` parameter to paginate through the results.
- [The Receive-Payment object](https://docs.conductor.is/api-ref/qbd/receive-payments/object.md): A receive-payment records when a payment is received from a customer *not* at the time of sale. It can be used for one or more of these purposes: (1) record a customer's payment against one or more invoices, (2) set a discount (e.g., for early payment), or (3) set a credit (e.g., from returned merchandise). Note: If full payment is received at the time of sale, use a sales receipt instead.
- [Retrieve a receive-payment](https://docs.conductor.is/api-ref/qbd/receive-payments/retrieve.md): Retrieves a receive-payment by ID.
- [Update a receive-payment](https://docs.conductor.is/api-ref/qbd/receive-payments/update.md): Updates an existing receive-payment.
- [Create a sales order](https://docs.conductor.is/api-ref/qbd/sales-orders/create.md): Creates a new sales order.
- [Delete a sales order](https://docs.conductor.is/api-ref/qbd/sales-orders/delete.md): Permanently deletes a a sales order. The deletion will fail if the sales order is currently in use or has any linked transactions that are in use.
- [List all sales orders](https://docs.conductor.is/api-ref/qbd/sales-orders/list.md): Returns a list of sales orders. Use the `cursor` parameter to paginate through the results.
- [The Sales Order object](https://docs.conductor.is/api-ref/qbd/sales-orders/object.md): A sales order tracks inventory that is on back order for a customer. In QuickBooks, sales orders and invoices use similar fields, and a sales order can be "converted" into an invoice (by linking the invoice to the sales order) once the inventory is in stock.
- [Retrieve a sales order](https://docs.conductor.is/api-ref/qbd/sales-orders/retrieve.md): Retrieves a sales order by ID.
- [Update a sales order](https://docs.conductor.is/api-ref/qbd/sales-orders/update.md): Updates an existing sales order.
- [Create a sales receipt](https://docs.conductor.is/api-ref/qbd/sales-receipts/create.md): Creates a new sales receipt.
- [Delete a sales receipt](https://docs.conductor.is/api-ref/qbd/sales-receipts/delete.md): Permanently deletes a a sales receipt. The deletion will fail if the sales receipt is currently in use or has any linked transactions that are in use.
- [List all sales receipts](https://docs.conductor.is/api-ref/qbd/sales-receipts/list.md): Returns a list of sales receipts. Use the `cursor` parameter to paginate through the results.
- [The Sales Receipt object](https://docs.conductor.is/api-ref/qbd/sales-receipts/object.md): A sales receipt records a sale where complete payment is received at the time of the transaction, whether by cash, check, or credit card. It combines the sale and payment into a single transaction. For situations requiring partial or delayed payments, use an invoice with receive-payments instead.
- [Retrieve a sales receipt](https://docs.conductor.is/api-ref/qbd/sales-receipts/retrieve.md): Retrieves a sales receipt by ID.
- [Update a sales receipt](https://docs.conductor.is/api-ref/qbd/sales-receipts/update.md): Updates an existing sales receipt.
- [Create a sales representative](https://docs.conductor.is/api-ref/qbd/sales-representatives/create.md): Creates a new sales representative.
- [List all sales representatives](https://docs.conductor.is/api-ref/qbd/sales-representatives/list.md): Returns a list of sales representatives. NOTE: QuickBooks Desktop does not support pagination for sales representatives; hence, there is no `cursor` parameter. Users typically have few sales representatives.
- [The Sales Representative object](https://docs.conductor.is/api-ref/qbd/sales-representatives/object.md): A sales representative is a person who can be assigned to sales transactions in QuickBooks Desktop. The sales representative corresponds to a separate employee, vendor, or other-name entity in QuickBooks.
- [Retrieve a sales representative](https://docs.conductor.is/api-ref/qbd/sales-representatives/retrieve.md): Retrieves a sales representative by ID.
- [Update a sales representative](https://docs.conductor.is/api-ref/qbd/sales-representatives/update.md): Updates an existing sales representative.
- [Create a sales-tax code](https://docs.conductor.is/api-ref/qbd/sales-tax-codes/create.md): Creates a new sales-tax code.
- [List all sales-tax codes](https://docs.conductor.is/api-ref/qbd/sales-tax-codes/list.md): Returns a list of sales-tax codes. NOTE: QuickBooks Desktop does not support pagination for sales-tax codes; hence, there is no `cursor` parameter. Users typically have few sales-tax codes.
- [The Sales-Tax Code object](https://docs.conductor.is/api-ref/qbd/sales-tax-codes/object.md): A sales tax code helps categorize items on a sales form as taxable or non-taxable, detailing reasons and associating tax codes with customers, items, or transactions.
- [Retrieve a sales-tax code](https://docs.conductor.is/api-ref/qbd/sales-tax-codes/retrieve.md): Retrieves a sales-tax code by ID.
- [Update a sales-tax code](https://docs.conductor.is/api-ref/qbd/sales-tax-codes/update.md): Updates an existing sales-tax code.
- [Create a sales-tax item](https://docs.conductor.is/api-ref/qbd/sales-tax-items/create.md): Creates a new sales-tax item.
- [List all sales-tax items](https://docs.conductor.is/api-ref/qbd/sales-tax-items/list.md): Returns a list of sales-tax items. Use the `cursor` parameter to paginate through the results.
- [The Sales-Tax Item object](https://docs.conductor.is/api-ref/qbd/sales-tax-items/object.md): A sales-tax item is an item used to calculate a single sales tax that is collected at a specified rate and paid to a single agency.
- [Retrieve a sales-tax item](https://docs.conductor.is/api-ref/qbd/sales-tax-items/retrieve.md): Retrieves a sales-tax item by ID.
- [Update a sales-tax item](https://docs.conductor.is/api-ref/qbd/sales-tax-items/update.md): Updates an existing sales-tax item.
- [Create a service item](https://docs.conductor.is/api-ref/qbd/service-items/create.md): Creates a new service item.
- [List all service items](https://docs.conductor.is/api-ref/qbd/service-items/list.md): Returns a list of service items. Use the `cursor` parameter to paginate through the results.
- [The Service Item object](https://docs.conductor.is/api-ref/qbd/service-items/object.md): A service item represents a billable service offered by or purchased by a business in QuickBooks Desktop. It can track both sales and purchases of services, with customizable pricing, descriptions, and tax settings. Common examples include professional services (consulting, legal advice), labor charges (installation, repairs), recurring services (maintenance contracts), and any non-physical items that generate revenue or expenses.
- [Retrieve a service item](https://docs.conductor.is/api-ref/qbd/service-items/retrieve.md): Retrieves a service item by ID.
- [Update a service item](https://docs.conductor.is/api-ref/qbd/service-items/update.md): Updates an existing service item.
- [Create a standard term](https://docs.conductor.is/api-ref/qbd/standard-terms/create.md): Creates a new standard term.
- [List all standard terms](https://docs.conductor.is/api-ref/qbd/standard-terms/list.md): Returns a list of standard terms. NOTE: QuickBooks Desktop does not support pagination for standard terms; hence, there is no `cursor` parameter. Users typically have few standard terms.
- [The Standard Term object](https://docs.conductor.is/api-ref/qbd/standard-terms/object.md): A standard term is a payment term that shows the number of days within which payment is due and can include a discount for early payment.
- [Retrieve a standard term](https://docs.conductor.is/api-ref/qbd/standard-terms/retrieve.md): Retrieves a standard term by ID.
- [Create a subtotal item](https://docs.conductor.is/api-ref/qbd/subtotal-items/create.md): Creates a new subtotal item.
- [List all subtotal items](https://docs.conductor.is/api-ref/qbd/subtotal-items/list.md): Returns a list of subtotal items. Use the `cursor` parameter to paginate through the results.
- [The Subtotal Item object](https://docs.conductor.is/api-ref/qbd/subtotal-items/object.md): A subtotal item calculates the sum of all items above it on a sales form, up to the previous subtotal. This is particularly important for applying discounts because discounts can only be applied to the line directly above them, requiring items to be subtotaled first.
- [Retrieve a subtotal item](https://docs.conductor.is/api-ref/qbd/subtotal-items/retrieve.md): Retrieves a subtotal item by ID.
- [Update a subtotal item](https://docs.conductor.is/api-ref/qbd/subtotal-items/update.md): Updates an existing subtotal item.
- [Create a time tracking activity](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/create.md): Creates a new time tracking activity.
- [Delete a time tracking activity](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/delete.md): Permanently deletes a a time tracking activity. The deletion will fail if the time tracking activity is currently in use or has any linked transactions that are in use.
- [List all time tracking activities](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/list.md): Returns a list of time tracking activities. Use the `cursor` parameter to paginate through the results.
- [The Time Tracking Activity object](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/object.md): A time tracking activity records billable or non-billable time spent by an employee, vendor, or other person on a specific service item, optionally associated with a customer or job for payroll and invoicing.
- [Retrieve a time tracking activity](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/retrieve.md): Retrieves a time tracking activity by ID.
- [Update a time tracking activity](https://docs.conductor.is/api-ref/qbd/time-tracking-activities/update.md): Updates an existing time tracking activity.
- [List all transactions](https://docs.conductor.is/api-ref/qbd/transactions/list.md): Searches across all transaction types. Unlike transaction-specific queries, this endpoint only returns fields common to all transaction types, such as ID, type, dates, account, and reference numbers. For more details specific to that transaction type, make a subsequent call to the relevant transaction-specific endpoint (such as invoices, bills, etc.). NOTE: This endpoint does not support time tracking activities.
- [The Transaction object](https://docs.conductor.is/api-ref/qbd/transactions/object.md): A transaction in QuickBooks Desktop represents a financial event such as an invoice, bill, payment, or deposit that affects accounts and is recorded in the company's financial records. This object is returned by endpoints that search across all transaction types, and therefore only has fields common to all transaction types, such as ID, type, dates, account, and reference numbers.
- [Retrieve a transaction](https://docs.conductor.is/api-ref/qbd/transactions/retrieve.md): Retrieves a transaction by ID.
- [Create a transfer](https://docs.conductor.is/api-ref/qbd/transfers/create.md): Creates a new transfer.
- [List all transfers](https://docs.conductor.is/api-ref/qbd/transfers/list.md): Returns a list of transfers. Use the `cursor` parameter to paginate through the results.
- [The Transfer object](https://docs.conductor.is/api-ref/qbd/transfers/object.md): A transfer records the movement of funds between two accounts in QuickBooks Desktop. It reduces the balance of one account (the "from" account) and increases the balance of another account (the "to" account) by the same amount. Transfers are commonly used for moving money between bank accounts or recording internal fund movements.
- [Retrieve a transfer](https://docs.conductor.is/api-ref/qbd/transfers/retrieve.md): Retrieves a transfer by ID.
- [Update a transfer](https://docs.conductor.is/api-ref/qbd/transfers/update.md): Updates an existing transfer.
- [Health check](https://docs.conductor.is/api-ref/qbd/utilities/health-check.md): Checks whether the specified QuickBooks Desktop connection is active and can process requests end-to-end. This is useful for showing a "connection status" indicator in your app. If an error occurs, the typical Conductor error response will be returned. As with any request to QuickBooks Desktop, the health check may fail if the application is not running, the wrong company file is open, or if a modal dialog is open. Timeout is 60 seconds.
- [Create a vendor credit](https://docs.conductor.is/api-ref/qbd/vendor-credits/create.md): Creates a new vendor credit.
- [Delete a vendor credit](https://docs.conductor.is/api-ref/qbd/vendor-credits/delete.md): Permanently deletes a a vendor credit. The deletion will fail if the vendor credit is currently in use or has any linked transactions that are in use.
- [List all vendor credits](https://docs.conductor.is/api-ref/qbd/vendor-credits/list.md): Returns a list of vendor credits. Use the `cursor` parameter to paginate through the results.
- [The Vendor Credit object](https://docs.conductor.is/api-ref/qbd/vendor-credits/object.md): A vendor credit (also known as a bill credit) represents money that a vendor owes back to your business, typically from overpayment or returned merchandise. When processing bill payments, you can apply these credits via `applyToTransactions.setCredit` to reduce payment amounts. Note that vendor credits track money owed by vendors, while credit memos track money you owe customers and are handled through receive-payment transactions.
- [Retrieve a vendor credit](https://docs.conductor.is/api-ref/qbd/vendor-credits/retrieve.md): Retrieves a vendor credit by ID.
- [Update a vendor credit](https://docs.conductor.is/api-ref/qbd/vendor-credits/update.md): Updates an existing vendor credit.
- [Create a vendor](https://docs.conductor.is/api-ref/qbd/vendors/create.md): Creates a new vendor.
- [List all vendors](https://docs.conductor.is/api-ref/qbd/vendors/list.md): Returns a list of vendors. Use the `cursor` parameter to paginate through the results.
- [The Vendor object](https://docs.conductor.is/api-ref/qbd/vendors/object.md): A vendor is any person or company from whom a small business owner buys goods and services. (Banks and tax agencies usually are included on the vendor list.) A company's vendor list contains information such as account balance and contact information about each vendor.
- [Retrieve a vendor](https://docs.conductor.is/api-ref/qbd/vendors/retrieve.md): Retrieves a vendor by ID.
- [Update a vendor](https://docs.conductor.is/api-ref/qbd/vendors/update.md): Updates an existing vendor.
- [Request IDs](https://docs.conductor.is/api-ref/request-ids.md)
- [SDKs](https://docs.conductor.is/api-ref/sdks.md): Check out Conductor's official Node.js and Python SDKs for integrating with the QuickBooks Desktop API.
- [Timeouts](https://docs.conductor.is/api-ref/timeouts.md)
- [Upgrading to Conductor's new Node.js SDK](https://docs.conductor.is/api-ref/upgrade-node.md): How to migrate from the old `conductor-node` package to Conductor's new Node.js SDK and QuickBooks Desktop API v2.
- [Welcome to Conductor's QuickBooks Desktop API](https://docs.conductor.is/api-ref/welcome.md)
- [What's new in API v2](https://docs.conductor.is/api-ref/whats-new.md): Conductor's new QuickBooks Desktop API is a massive leap forward and sets the foundation for future integrations.
- [Send a passthrough request to an end-user's integration connection](https://docs.conductor.is/apis/end-users/passthrough.md): Sends a request directly to the specified integration connection (e.g., QuickBooks Desktop) on behalf of the end-user.
- [Product updates](https://docs.conductor.is/changelog/overview.md): New updates and improvements.
- [FAQ](https://docs.conductor.is/faq.md): The fundamentals of Conductor.
- [How to fix: "Another update is in progress"](https://docs.conductor.is/help/connection-errors/another-update-in-progress.md)
- [How to fix: "Your QuickBooks Desktop connection is not active"](https://docs.conductor.is/help/connection-errors/connection-not-active.md)
- [How to fix: "Could not start QuickBooks"](https://docs.conductor.is/help/connection-errors/could-not-start-quickbooks.md)
- [How to fix: "Do you want to allow this app to make changes to your device?"](https://docs.conductor.is/help/connection-errors/do-you-want-to-allow-this-app-to-make-changes-to-your-device.md)
- [How to fix: "QBWC1039: This application has not accessed this QuickBooks company data file before"](https://docs.conductor.is/help/connection-errors/qbd-admin-required.md)
- [How to fix: "QBWC1085: There was a problem with the log file"](https://docs.conductor.is/help/connection-errors/there-was-a-problem-with-the-log-file.md)
- [How to fix: "Unable to cast COM object of type System.__ComObject..."](https://docs.conductor.is/help/connection-errors/unable-to-cast-com-object.md)
- [How to fix: "QBWC1039: Unique OwnerID/FileID pair value required"](https://docs.conductor.is/help/connection-errors/unique-owner-id-file-id-pair-required.md)
- [QuickBooks Desktop FAQ for end-users](https://docs.conductor.is/help/faq/end-user-faq.md): An FAQ guide written for the end-users of Conductor's QuickBooks Desktop integration.
- [How to disable the QuickBooks Desktop connection](https://docs.conductor.is/help/guides/disable-connection.md): Learn how to disable the QuickBooks Desktop connection in the QuickBooks Web Connector.
- [Ensure only one version of QuickBooks Desktop is installed](https://docs.conductor.is/help/guides/ensure-only-one-version-installed.md): Conductor will not function properly if multiple versions of QuickBooks Desktop are installed on the same computer.
- [Ensure the QuickBooks Web Connector opens at startup](https://docs.conductor.is/help/guides/ensure-web-connector-opens-at-startup.md): Set up the QuickBooks Web Connector to automatically launch and run in the background whenever you start your computer.
- [Move your QuickBooks Desktop connection to a different computer](https://docs.conductor.is/help/guides/move-computers.md): Learn how to transfer your QuickBooks Desktop connection to a different computer for the same company file.
- [Connect to multiple QuickBooks company files on a single computer](https://docs.conductor.is/help/guides/multiple-connections-one-computer.md): How to connect to multiple QuickBooks Desktop company files on a single computer.
- [Rebuild and verify Data in QuickBooks Desktop](https://docs.conductor.is/help/guides/rebuild-verify-data.md): Learn how to use the Rebuild and Verify Data utilities to fix and identify data issues in your QuickBooks Desktop company file.
- [Connecting to QuickBooks Desktop on Rightworks](https://docs.conductor.is/help/guides/rightworks.md): How to set up the QuickBooks Web Connector when using Rightworks (formerly Right Networks) to host QuickBooks Desktop.
- [Upgrade the QuickBooks Web Connector](https://docs.conductor.is/help/guides/upgrade-web-connector.md): Learn how to upgrade the QuickBooks Web Connector to ensure compatibility with Conductor.
- [Invoices](https://docs.conductor.is/qbd-examples/invoices.md)
- [Jobs and customers](https://docs.conductor.is/qbd-examples/jobs-and-customers.md): Understanding the relationship between customers and jobs in QuickBooks Desktop.
- [Refunds](https://docs.conductor.is/qbd-examples/refunds.md)
- [Get a free QuickBooks Desktop developer license](https://docs.conductor.is/qbd/dev-license.md): Obtain a free Not-For-Resale (NFR) license to use QuickBooks Desktop indefinitely for development and testing with Conductor. Takes 2 minutes.
- [Mapping your app's objects to QuickBooks Desktop](https://docs.conductor.is/qbd/mapping-objects.md): How to handle syncing your application's data objects with QuickBooks Desktop objects.
- [Create a QuickBooks Desktop test instance](https://docs.conductor.is/qbd/test-instance.md): Create your own QuickBooks Desktop test instance with an AWS WorkSpace.
- [Quickstart](https://docs.conductor.is/quickstart.md): Connect to QuickBooks Desktop via a modern API in 5 minutes.
- [Security and data retention](https://docs.conductor.is/security.md): Conductor's policies on security, data retention, and data processing.
- [Connect your users via the auth flow](https://docs.conductor.is/usage/auth-flow.md): Enable your users to securely connect their QuickBooks Desktop to your application.
- [Autocomplete](https://docs.conductor.is/usage/autocomplete.md): Use Conductor's built-in autocomplete to discover all available APIs, requests parameters, and response fields.
- [Check an end-user's connection status](https://docs.conductor.is/usage/connection-status.md)
- [Error handling](https://docs.conductor.is/usage/error-handling.md): Catch and respond to connection errors, invalid requests, data problems, and more.
- [API keys](https://docs.conductor.is/usage/keys.md): Use API keys to authenticate API requests.
- [Terminology](https://docs.conductor.is/usage/terminology.md): The key concepts you should know to use Conductor.
