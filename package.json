{"name": "llms-txt-hub", "version": "1.0.0", "private": true, "scripts": {"build": "node scripts/search-index-generator.cjs && turbo build", "check:frontmatter": "tsx scripts/check-frontmatter.ts", "check:links": "lychee --config ./lychee.toml --verbose .", "clean": "git clean -xdf node_modules dist .next", "clean:workspaces": "turbo clean", "commit": "cz", "dev": "cross-env FORCE_COLOR=1 turbo dev --parallel", "format": "biome check --write .", "generate-search-index": "node scripts/search-index-generator.js", "generate-llms-chatbot-index": "node scripts/llms-chatbot-index-generator.js", "generate-websites": "tsx scripts/generate-websites.ts", "generate:llms": "pnpm --filter generator start", "generate:website": "turbo gen website", "postinstall": "manypkg fix", "lint": "turbo lint && manypkg check", "lint:fix": "turbo lint:fix && manypkg fix && pnpm biome lint --write .", "shadcn": "cd packages/design-system && pnpm dlx shadcn@canary add", "start": "turbo run start", "supabase:migrate": "supabase db reset && supabase db push", "supabase:migrate:prod": "supabase db push", "supabase:seed": "supabase db reset --db-url=$DATABASE_URL", "supabase:seed:users": "pnpm --filter @thedaviddias/supabase seed:users", "supabase:typegen": "pnpm --filter @thedaviddias/supabase supabase:typegen", "supabase:reset": "pnpm --filter @thedaviddias/supabase supabase:reset", "supabase:start": "pnpm --filter @thedaviddias/supabase supabase:start", "supabase:stop": "pnpm --filter @thedaviddias/supabase supabase:stop", "syncpack:fix": "pnpm dlx syncpack fix-mismatches", "syncpack:list": "pnpm dlx syncpack list-mismatches", "test": "turbo test --cache-dir=.turbo", "test:watch": "turbo test:watch --cache-dir=.turbo", "test:e2e": "turbo test:e2e --filter=e2e", "test:e2e:ui": "turbo test:e2e:ui --filter=e2e", "test:e2e:debug": "turbo test:e2e:debug --filter=e2e", "typecheck": "turbo typecheck", "update": "pnpm update -r", "update-llms-list": "node scripts/update-llms-list.js"}, "devDependencies": {"@auto-it/all-contributors": "^11.3.0", "@auto-it/first-time-contributor": "^11.3.0", "@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@content-collections/mdx": "^0.2.2", "@manypkg/cli": "^0.23.0", "@turbo/gen": "^2.4.4", "@types/js-yaml": "^4.0.9", "chalk": "^5.4.1", "cross-env": "^7.0.3", "glob": "^11.0.1", "gray-matter": "^4.0.3", "js-yaml": "^4.1.0", "lefthook": "^1.11.6", "turbo": "^2.4.4", "typescript": "^5.8.2"}, "packageManager": "pnpm@10.7.1", "engines": {"node": ">=22"}}